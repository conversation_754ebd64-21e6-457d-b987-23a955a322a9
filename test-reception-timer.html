<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>接待时长测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .timer-display {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #f0f2f5;
            border-radius: 4px;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background: #165DFF;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 0 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056e6;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .warning-message {
            background: #fff7e6;
            border: 1px solid #ffd591;
            color: #d48806;
            padding: 12px 16px;
            border-radius: 4px;
            margin: 10px 0;
            position: relative;
            display: none;
        }
        .warning-message.show {
            display: block;
        }
        .close-btn {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            font-size: 16px;
            cursor: pointer;
            color: #d48806;
            padding: 0;
            margin: 0;
        }
        .close-btn:hover {
            color: #ad6800;
        }
        .info {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            color: #0050b3;
            padding: 12px 16px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>接待时长测试</h1>
        <div class="info">
            <strong>测试说明：</strong>
            <ul>
                <li>点击"开始接待"按钮开始计时</li>
                <li>当接待时长达到25分钟（1500秒）时，会显示警告提示</li>
                <li>可以通过点击关闭按钮、按ESC键来关闭提示</li>
                <li>为了测试方便，可以点击"快进到25分钟"按钮</li>
            </ul>
        </div>
        
        <div class="timer-display" id="timerDisplay">00:00:00</div>
        
        <div class="controls">
            <button id="startBtn" onclick="startReception()">开始接待</button>
            <button id="stopBtn" onclick="stopReception()" disabled>停止接待</button>
            <button id="fastForwardBtn" onclick="fastForward()" disabled>快进到25分钟</button>
            <button onclick="resetTimer()">重置</button>
        </div>
        
        <div id="warningMessage" class="warning-message">
            根据业务要求，接待时长不宜超过30分钟，请尽快结束当前通话！
            <button class="close-btn" onclick="closeWarning()">&times;</button>
        </div>
    </div>

    <script>
        let receptionTimer = null;
        let receptionSecond = 0;
        let receptionStartTime = null;
        let has25MinuteWarningShown = false;
        
        function formatTime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        
        function updateDisplay() {
            document.getElementById('timerDisplay').textContent = formatTime(receptionSecond);
        }
        
        function handleReceptionTimer() {
            if (receptionStartTime) {
                receptionSecond = Math.floor((Date.now() - receptionStartTime) / 1000);
                updateDisplay();
                
                // 检查是否超过25分钟（1500秒）且未显示过提示
                if (receptionSecond >= 1500 && !has25MinuteWarningShown) {
                    has25MinuteWarningShown = true;
                    showWarning();
                }
            }
        }
        
        function showWarning() {
            const warningElement = document.getElementById('warningMessage');
            warningElement.classList.add('show');
            
            // 添加ESC键监听器
            document.addEventListener('keydown', handleEscKey);
        }
        
        function closeWarning() {
            const warningElement = document.getElementById('warningMessage');
            warningElement.classList.remove('show');
            document.removeEventListener('keydown', handleEscKey);
        }
        
        function handleEscKey(event) {
            if (event.key === 'Escape') {
                closeWarning();
            }
        }
        
        function startReception() {
            receptionStartTime = Date.now();
            receptionSecond = 0;
            has25MinuteWarningShown = false;
            
            receptionTimer = setInterval(handleReceptionTimer, 1000);
            
            document.getElementById('startBtn').disabled = true;
            document.getElementById('stopBtn').disabled = false;
            document.getElementById('fastForwardBtn').disabled = false;
            
            closeWarning();
            updateDisplay();
        }
        
        function stopReception() {
            if (receptionTimer) {
                clearInterval(receptionTimer);
                receptionTimer = null;
            }
            
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            document.getElementById('fastForwardBtn').disabled = true;
            
            closeWarning();
        }
        
        function fastForward() {
            // 快进到24分50秒，这样10秒后就会触发25分钟警告
            receptionStartTime = Date.now() - (24 * 60 + 50) * 1000;
            has25MinuteWarningShown = false;
        }
        
        function resetTimer() {
            stopReception();
            receptionSecond = 0;
            receptionStartTime = null;
            has25MinuteWarningShown = false;
            updateDisplay();
            closeWarning();
        }
        
        // 初始化显示
        updateDisplay();
    </script>
</body>
</html>
