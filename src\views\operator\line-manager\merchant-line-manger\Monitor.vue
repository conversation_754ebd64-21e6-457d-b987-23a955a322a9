<template>
  <div class="line-manger-container">
    <div class="tw-w-full tw-p-[16px] tw-pb-[8px] tw-grow-0 tw-shrink-0 tw-bg-white">
      <div class="tw-grid tw-grid-cols-5 2xl:tw-grid-cols-7 tw-gap-3 tw-border-b-[1px] tw-pb-[8px]">
        <div class="item">
          <el-input
            v-model.trim="searchForm.tenantLineName"
            placeholder="商户线路名称"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.tenantLineNumber"
            placeholder="商户线路编号"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.tenantName"
            placeholder="商户名称"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.tenantNumber"
            placeholder="商户编号"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-select
            v-model="searchForm.groupId"
            clearable
            filterable
            placeholder="所属账号"
            style="width: 100%;"
          >
            <el-option
              v-for="item in masterAccountList || []"
              :key="item.groupId"
              :label="item.account"
              :value="item.groupId"
            />
          </el-select>
        </div>
      </div>
      <div class="tw-flex tw-justify-between tw-items-center tw-mt-[12px]">
        <el-radio-group v-model="searchForm.recentMin"  @change="search">
          <el-radio-button v-for="item in timeRangeList" :label="item.value" :key="item.value">{{ item.label }}</el-radio-button>
        </el-radio-group>
        <div class="tw-flex">
          <ColumnSetting
            :totalList="totalCols"
            :defaultList="defaultCols"
            :disabledList="disabledCols"
            :name="'tenant-monitor'"
          />
          <el-button type="primary" @click="clearSearchForm" link>
            <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
            <span>重置</span>
          </el-button>
          <el-button type="primary" @click="search" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
        </div>
      </div>
    </div>
    <TenantMonitorTable :table-data="tableData || []" :recentMin="searchForm.recentMin" @update:table="search" showPagination :loading="!!loading">
      <template v-slot:operate="{row}">
        <el-button type="primary" link @click="goEdit(row)">编辑</el-button>
        <el-button type="primary" link @click="goDetail(row)">详情</el-button>
      </template>
    </TenantMonitorTable>
  </div>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, onActivated, onDeactivated, } from 'vue'
import { ElMessage, } from 'element-plus'
import { TenantParam, TenantMonitorInfoItem, } from '@/type/line'
import { lineMerchantModel } from '@/api/line'
import TenantMonitorTable from '../components/TenantMonitorTable.vue'
import { goMerchantLineDetail } from '@/utils/line'
import router from '@/router'
import to from 'await-to-js'
import { timeRangeList } from '../components/constant'
import { getColumnSettingByName} from '../components/constant'
import ColumnSetting from '@/components/ColumnSetting.vue'
import { monitorStatisticModel } from '@/api/monitor-statistic'

const loading = ref(0)
// 表格数据及分页
const tableData = ref<TenantMonitorInfoItem[] | null>([])
const {totalCols, defaultCols, disabledCols} = getColumnSettingByName('tenant-monitor')
// 搜索部分
class searchOrigin {
  tenantLineName = undefined
  tenantLineNumber = undefined
  tenantName = undefined
  tenantNumber = undefined
  groupId = undefined
  recentMin = 5
}
const searchForm = reactive<TenantParam>(new searchOrigin())

// 重置搜索数据
const clearSearchForm = () => {
  Object.assign(searchForm, new searchOrigin())
  ElMessage({
    type: 'success',
    message: '重置成功'
  })
}
// 搜索
const search = async () => {
  loading.value++
  const [_, res] = await to(lineMerchantModel.getMonitorList({
    ...searchForm,
    recentMin: searchForm.recentMin ? searchForm.recentMin : null
  })) as [Error | null, TenantMonitorInfoItem[]]
  tableData.value = (res||[]).sort((a, b) => b.currentlyCallNum - a.currentlyCallNum)
  loading.value--
}
// 列表进入详情
const goDetail = (row: TenantMonitorInfoItem) => {
  router.push({
    name: 'MerchantLineMonitorDetails',
    query: {
      tenantLineNumber: row.tenantLineNumber,
      tenantLineName: row.tenantLineName,
      tenantNumber: row.tenantNumber,
      account: row.account,
    }
  })
}

const masterAccountList = ref<{
  account: string,
  groupId: string,
}[] | null>([]) // 主账号列表，全量

// 编辑当前商户线路
// 表格 -- 前往【详情】
const goEdit =async (row: TenantMonitorInfoItem) => {
  goMerchantLineDetail(row)
}
// 【定时更新】每隔5分钟触发刷新，【进入刷新】包括tab切换和浏览器标签切换，触发刷新
// 定时器
const timer = ref()
// 浏览器标签切换，触发刷新
const handleVisibilityChange = () => {
  if (document.visibilityState === 'visible') {
    search()
  }
}
onActivated(async () => {
  const [err1, res1] = await to(monitorStatisticModel.getAllMainAccount())
  masterAccountList.value = (res1 || []).filter(item => !!item.account && !!item.groupId)
  search()
  document.addEventListener('visibilitychange', handleVisibilityChange)
  timer.value = setInterval(() => {
    search()
  }, 5 * 60 * 1000)
})
// 移除定时器和监听器
onDeactivated(() => {
  clearInterval(timer.value)
  timer.value = null
  masterAccountList.value = null
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  tableData.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.line-manger-container {
  width: 100%;
  box-sizing: border-box;
  height: calc(100% - 48px);
  display: flex;
  flex-direction: column;
  .item {
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .label {
      width: 120px
    }
    :deep(.el-input__wrapper) {
      width: 100%;
    }
    .el-select {
      width: 100%;
    }
  }
  .el-table {
    font-size: 13px;
    :deep(.cell) {
      padding: 0 8px;
    }
  }
  .table-btn-box {
    display: flex;
    .el-button {
      width: 60px;
    }
  }
  :deep(.el-table .caret-wrapper) {
    display: none;
  }
}
</style>
