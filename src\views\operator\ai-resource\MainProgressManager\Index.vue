<template>
  <HeaderBox :title="titleList" />
  <div class="process-container" v-loading="loading">
    <div class="tw-flex tw-justify-between tw-items-center">
      <div class="tw-text-left tw-text-[14px] tw-flex tw-items-center">
        <el-icon class="tw-mr-1 tw-cursor-pointer" @click="goBack"><ArrowLeftBold /></el-icon>
        <span class="tw-font-semibold">{{ scriptStore.name }}</span>
        <span class="status-box-mini blue-status tw-ml-0.5">
          {{ industryMapList?.find(item => item.id === scriptStore.secondIndustryId)?.name || '' }}
        </span>
        <span class="status-box-mini orange-status tw-ml-0.5">
          {{ multiContentVersion ? 'v2.0' : 'v1.0' }}
        </span>
        <el-icon v-if="!isChecked" class="tw-cursor-pointer tw-ml-0.5" :size="16" color="var(--el-color-primary)" @click="edit">
          <SvgIcon name="edit2"></SvgIcon>
        </el-icon>
        <span class="tw-ml-[24px] tw-text-[13px]">
          备注：{{scriptStore.remark || '-'}}
        </span>
        <el-icon v-if="!isChecked" class="tw-cursor-pointer tw-ml-0.5" :size="14" color="var(--el-color-primary)" @click="editRemark">
          <SvgIcon name="edit2"></SvgIcon>
        </el-icon>
      </div>
      <div v-if="!isChecked" class="tw-flex tw-items-center">
        <el-button :loading="loadingPublish"  class="tw-ml-1" type="primary" @click="confirmPublish" :icon="Select">
          提交审核
        </el-button>
        <el-button class="tw-ml-1" @click="exitEdit" :icon="CloseBold">
          退出编辑
        </el-button>
      </div>
    </div>
    <div class="script-container">
      <TabsBox v-model:active="active" :tabList="scriptTabs">
        <!--话术编辑时显示-->
        <div v-if="!scriptStore.isChecked" class="tw-flex tw-justify-center tw-items-center tw-ml-auto tw-mr-[8px]">
          <el-button size="small" @click="onClickCheckSemantics">
            语义检查
          </el-button>
        </div>
      </TabsBox>
      <keep-alive :max="4">
        <MainProgress v-if="active == '主动流程'"/>
        <Knowledge v-else-if="active == '知识库'"/>
        <FunctionalWords v-else-if="active == '功能话术'"/>
        <BranchManager v-else-if="active == '分支管理'"/>
        <IntentionManager v-else-if="active == '意向'"/>
        <AudioManager v-else-if="active == '音频'"/>
        <Training v-else-if="active == '话术训练'" />
        <Statistic v-else-if="active == '统计' && status && [SpeechCraftStatusEnum['生效中'], SpeechCraftStatusEnum['已停用']].includes(status)"/>
        <Setting v-else-if="active == '设置'"/>
        <Log v-else-if="active == '日志'"/>
        <el-empty v-else></el-empty>
      </keep-alive>
    </div>
  </div>
  <AddDialog
    v-model:visible="addVisible"
    :editData="editData"
    @confirm="confirmScriptInfoUpdate"
  />
  <CheckSemanticsDialog
    v-model:visible="checkSemanticsDialogVisible"
    :secondIndustryId="scriptStore.secondIndustryId"
    :scriptId="scriptStore.id"
  />
  <EditRemarkDialog
    v-model:visible="editScriptRemarkVisible"
    :data="editData"
    @confirm="confirmScriptInfoUpdate"
  ></EditRemarkDialog>
</template>

<script lang="ts" setup>
import { computed, defineAsyncComponent, onUnmounted, ref, onMounted } from 'vue'
import { scriptTableModel, scriptTrainCommonModel, scriptAudioModel } from '@/api/speech-craft'
import { ArrowLeftBold, CloseBold, Select, } from '@element-plus/icons-vue'
import { useScriptStore } from '@/store/script'
import { ElMessage } from 'element-plus'
import { router } from '@/router'
import { SpeechCraftInfoItem, SpeechCraftStatusEnum } from '@/type/speech-craft'
import AddDialog from '@/views/operator/ai-resource/SpeechCraftManager/AddDialog.vue'
import EditRemarkDialog from '@/views/operator/ai-resource/SpeechCraftManager/EditRemarkDialog.vue'
import { storeToRefs } from 'pinia'
import { useGlobalStore } from '@/store/globalInfo'
import Confirm from '@/components/message-box'
import HeaderBox from '@/components/HeaderBox.vue'
import TabsBox from '@/components/TabsBox.vue'
import { trace } from '@/utils/trace';
import to from 'await-to-js'

const MainProgress = defineAsyncComponent({loader: () => import('./MainProcess/Index.vue')})
const IntentionManager = defineAsyncComponent({loader: () => import('./IntentionManager/Index.vue')})
const Knowledge = defineAsyncComponent({loader: () => import('./Knowledge/Index.vue')})
const AudioManager = defineAsyncComponent({loader: () => import('./AudioManager/Index.vue')})
const BranchManager = defineAsyncComponent({loader: () => import('./Branch/Index.vue')})
const FunctionalWords = defineAsyncComponent({loader: () => import('./FunctionalWords/Index.vue')})
const Training = defineAsyncComponent({loader: () => import('@/views/operator/ai-resource/MainProgressManager/Train/Index.vue')})
const Statistic = defineAsyncComponent({loader: () => import('./Statistic/Index.vue')})
const Log = defineAsyncComponent({loader: () => import('./Log/Index.vue')})
const Setting = defineAsyncComponent({loader: () => import('./Setting/Index.vue')})
const CheckSemanticsDialog = defineAsyncComponent({loader: () => import('./CheckSemanticsDialog.vue')})
const globalStore = useGlobalStore()
const { loading } = storeToRefs(globalStore)
const loadingPublish = ref(false)
const scriptStore = useScriptStore()
const id = scriptStore.id
const isChecked = scriptStore.isChecked
const multiContentVersion = scriptStore.multiContentVersion
const status = scriptStore.status
const scriptTabs = ref<string[]>([])
if (status && [SpeechCraftStatusEnum['生效中'], SpeechCraftStatusEnum['已停用']].includes(status)) {
  scriptTabs.value = ['主动流程', '知识库', '功能话术', '分支管理', '意向', '音频', '话术训练', '统计', '设置', '日志']
} else {
  scriptTabs.value = ['主动流程', '知识库', '功能话术', '分支管理', '意向', '音频', '话术训练', '设置', '日志']
}
const active = ref<string>(scriptTabs.value[0] || '主动流程')
const addVisible = ref(false)
const titleList = computed(() => [{title: '话术制作',}, {title: '话术编辑'},])

const industryMapList = globalStore.getIndustryIdAndNameMapList

const editData = ref<SpeechCraftInfoItem | null>(null)
const confirmFunc = async () => {
  loading.value = true

  // 保存最新的画布
  const [err1] = await to(scriptStore.saveGraph(false, true))
  if (err1) {
    loading.value = false
    return
  }

  const params = {
    scriptId: id,
    scriptName: scriptStore.name,
  }
  await trace({
    page: `话术编辑(${id})-提交审核:开始`,
    params: params,
  })
  const [err2] = await to(scriptTableModel.publishScriptTable(params))
  await trace({
    page: `话术编辑(${id})-提交审核:完成`,
    params: err1,
  })

  if (!err2) {
    ElMessage.success('发布成功')
    scriptStore.clearScriptData()
    router.push({
      name: 'SpeechCraftCheck',
    })
  }
  loading.value = false
}
const confirmPublish = async () => {
  loadingPublish.value = true

  
  if (!scriptStore.name.includes('测试') && !scriptStore.name.includes('test')) {
    // 非测试话术备注必填
    if (!scriptStore.remark) {
      ElMessage.warning('未设置话术备注，不允许提交')
      loadingPublish.value = false
      return 
    }
    // 非测试话术必须话术训练
    const [err1, res1] = await to(scriptTrainCommonModel.getTrainHistory(scriptStore.scriptStringId))
    if (!res1 || !res1?.length) {
      ElMessage.warning('未进行过话术训练，不允许提交')
      loadingPublish.value = false
      return 
    }
  }
  // 检查音频上传
  const [err2, res2] = await to(scriptAudioModel.findAudioList({scriptId: scriptStore.id}))
  const { totalNums = 0, uploadNums = 0 } = res2 || {}
  if (totalNums !== uploadNums || !!err2) {
    ElMessage.warning('音频未上传完成，不允许提交')
    loadingPublish.value = false
    return 
  }
  const [errConfirm] = await to(Confirm({
    text: `<p>您确定要提交审核【${scriptStore.name}】吗?</p>
    <p style="margin-top:6px;color:#E54B17;font-weight:600;">话术提交审核并通过审核后，会先进入预发布状态，等待音频全部同步完成后状态会变为生效中。</p>`,
    type: 'warning',
    title: '发布确认',
    confirmText: '我已知悉'
  }))
  loadingPublish.value = false
  if (errConfirm) {
    return
  }
  confirmFunc()
}
const edit = async () => {
  loading.value = true
  const data = await scriptTableModel.findOneScriptById({id}) as SpeechCraftInfoItem
  loading.value = false
  editData.value = data || null
  addVisible.value = true
}

// 编辑备注
const editScriptRemarkVisible = ref(false)
const editRemark = async () => {
  loading.value = true
  const [err, data] = await to(scriptTableModel.findOneScriptById({id}))
  loading.value = false
  editData.value = data || null
  editScriptRemarkVisible.value = true
}
const confirmScriptInfoUpdate = async () => {
  editScriptRemarkVisible.value = false
  addVisible.value = false
  loading.value = true
  const [err, data] = await to(scriptTableModel.findOneScriptById({id}))
  if (!data) {
    loading.value = false
    return ElMessage.error('获取话术信息失败')
  }
  scriptStore.name = data.scriptName;
  scriptStore.primaryIndustry = data.primaryIndustry || '';
  scriptStore.secondIndustryId = data.secondaryIndustryId || -1;
  scriptStore.multiContentVersion = data.multiContentVersion ?? false;
  scriptStore.remark = data.remark ?? '';
  scriptStore.getSemanticOptions(true)
  loading.value = false
}

// 语义检查弹窗，是否显示
const checkSemanticsDialogVisible = ref(false)
/**
 * 点击语义检查按钮
 */
const onClickCheckSemantics = () => {
  // 显示语义检查弹窗
  checkSemanticsDialogVisible.value = true
}
// 执行区

// 返回话术列表
const goBack = () => {
  router.push({
    name: 'SpeechCraftManager',
    query: {
      isBack: 1,
    }
  })
}
// 退出编辑
const exitEdit = async () => {
  if (!scriptStore.isChecked) {
    await scriptStore.unLockSpeechCraft(scriptStore.id)
  }
  goBack()
}

onMounted(() => {
  confirmScriptInfoUpdate()
})

onUnmounted(() => {
  scriptStore.clearScriptData()
})

</script>

<style scoped lang="postcss" type="text/postcss">
.process-container {
  margin: 16px;
  min-width: 1040px;
  height: 100%;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
  /* background-color: #fff; */
  display: flex;
  flex-direction: column;
  .status-box-mini {
    width: auto;
    min-width: 40px;
    padding: 0 4px;
  }
}
.script-container {
  margin-top: 12px;
  flex-grow: 1;
}
</style>
