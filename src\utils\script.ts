import {
  CorpusTypeEnum, AudioResponse, ScriptMultiContent, scriptUnitContent,
  ContentDataOrigin, AudioItem, AudioStatusEnum
} from '@/type/speech-craft'
import { readXlsx, } from '@/utils/export'
import { scriptAudioModel, } from '@/api/speech-craft'
import { useScriptStore } from '@/store/script'
import { ElMessage } from 'element-plus';
import to from 'await-to-js';


/**
 * 处理语料文字内容
 * @param {ScriptMultiContent[] | null | undefined} scriptMultiContents - 语句数组
 * @param {number} scriptId - 话术id
 * @param {number} [corpusId] - 语料id
 * @returns {ScriptMultiContent[]} 返回处理后的语句数组
 */
export const handleCorpusMultiContent = (
  scriptMultiContents: ScriptMultiContent[] | null | undefined,
  scriptId: number,
  corpus: {
    id?: number,
    corpusType: CorpusTypeEnum,
  },
  showDetails: boolean = false
) => {
  if (!scriptMultiContents || !scriptMultiContents.length) {
    return [{
      scriptUnitContents: [new ContentDataOrigin(corpus.corpusType, corpus.id)],
      corpusId: corpus.id || undefined,
      scriptId: scriptId,
      deleted: false
    }]
  }
  return scriptMultiContents.map(item => {
    let orders = 0
    return {
      id: item.id,
      scriptUnitContents: item.scriptUnitContents?.length > 0 ? item.scriptUnitContents.map(v => {
        orders++
        return {
          id: v.id,
          content: v.content,
          audioPath: showDetails ? v.audioPath : undefined,
          contentName: showDetails ? v.contentName : undefined,
          corpusId: v.corpusId || corpus.id,
          audioStatus: v.audioStatus,
          deleted: v.deleted,
          contentType: v.contentType,
          orders: orders,
        }
      }) : [new ContentDataOrigin(corpus.corpusType, item.corpusId || corpus.id)],
      corpusId: item.corpusId || corpus.id,
      scriptId: scriptId,
      deleted: item.deleted,
    }
  })
}

/**
 * 比较两个语料文字内容
 * 格式化语料1的语句， 将语料2多余的语句deleted设置为true,将语料1多余的语句id置空
 * 主要用于画布回滚操作和编辑语料（连接语料切换挂机和非挂机）
 * @param {ScriptMultiContent[]} multiContents1 - 语料1（新）
 * @param {ScriptMultiContent[] | undefined} multiContents2 - 语料2（旧）
 * @returns {ScriptMultiContent[]}
 */
export const compareCorpusMultiContent = (
  multiContents1: ScriptMultiContent[],
  multiContents2: ScriptMultiContent[] | undefined,
  corpus: {
    id?: number,
    corpusType: CorpusTypeEnum,
  },
) : ScriptMultiContent[] => {
  const scriptStore = useScriptStore()
  const scriptId = scriptStore.id
  const contents1: scriptUnitContent[] = handleCorpusMultiContent(multiContents1, scriptId, corpus)[0]?.scriptUnitContents || []
  const contents2: scriptUnitContent[] = handleCorpusMultiContent(multiContents2, scriptId, corpus)[0]?.scriptUnitContents || []
  const content1Ids = contents1 && contents1.length ? contents1?.map(i => i.id) : []
  const content2Ids = contents2 && contents2.length ? contents2?.map(i => i.id) : []
  // 删除语句
  const deletedArr = contents2.flatMap(item => {
    if (item.id && !content1Ids.includes(item.id) && item.content) {
      return [{
        content: item.content,
        corpusId: item.corpusId,
        audioStatus: item.audioStatus,
        deleted: true,
        corpusType: item.corpusType,
        id: item.id
      }]
    } else {
      return []
    }
  })
  // 已有语句长度
  let exsitLen = 0
  // 新增+已有语句
  const exsitAndNewArr = contents1.flatMap((item, orders) => {
    if (item.content) {
      if (item.id && content2Ids.includes(item.id)) {
        exsitLen++
        return [{
          content: item.content,
          corpusId: item.corpusId,
          audioStatus: item.audioStatus,
          id: item.id,
          corpusType: item.corpusType,
          orders,
        }]
      } else {
        return [{
          content: item.content,
          corpusId: item.corpusId,
          audioStatus: item.audioStatus,
          corpusType: item.corpusType,
          orders,
        }]
      }
    } else {
      return []
    }
  })
  /**
   * corpusId：如当前不存在从语料信息获取
   * id：当存在删除、已有语句时，获取id（新的没有，从旧的获取）
   * scriptId：获取话术id（新的没有，从语料信息获取）
   * deleted：文字内容层面的逻辑删除字段，如果（新增+已有语句）空，设置为true
   */
  return [{
    scriptUnitContents: [...exsitAndNewArr, ...deletedArr],
    corpusId: multiContents1[0] && multiContents1[0].corpusId || corpus.id,
    id: deletedArr.length > 0 || exsitLen > 0 ? multiContents1[0]?.id || (multiContents2 ? multiContents2[0]?.id : undefined) : undefined,
    scriptId: multiContents1[0]?.scriptId || scriptId,
    deleted: exsitAndNewArr.length > 0 ? multiContents1[0]?.deleted : true,
  }]
}

/**
 * 将文字内容中的语料和id信息全部去除
 */
export const removeInfoInMultiContent = (scriptMultiContents: ScriptMultiContent[]) => {
  return scriptMultiContents?.map(item => {
    return {
      scriptUnitContents: item.scriptUnitContents?.map(v => {
        v.corpusId = undefined
        v.id = undefined
        v.audioStatus = AudioStatusEnum['未验听']
        return v
      }) || [],
      corpusId: undefined,
      id: undefined,
      scriptId: item.scriptId,
      deleted: false,
    }
  }) || []
}

const exportTitleList1 = ['语料名称', '文字内容']
export const checkXlsContent = async (e: Event) => {
  // 获取话术缓存信息
  const scriptStore = useScriptStore()
  const scriptId = scriptStore.id

  // 读取xls
  const { data } = await readXlsx(e) as {
    data: Record<string, any>[]
  }

  // 检查xls表头是否正确
  if (data?.length > 0) {
    const list1 = Object.keys(data[0]!)
    if (list1.length !== new Set([...list1, ...exportTitleList1]).size) {
      ElMessage.warning('表头行格式错误，请检查')
      return null
    }
  }

  // 获取全部语料
  const [_, res] = await to(scriptAudioModel.findAudioList({
    scriptId: scriptId,
  })) as [any, AudioResponse]
  const { scriptUnitContents, totalNums = 0, uploadNums = 0 } = res || {
    scriptUnitContents: [],
    totalNums: 0,
    uploadNums: 0,
  }
  const scriptCorpusObj: Record<string, AudioItem> = {}
  scriptUnitContents?.forEach(item => {
    // @ts-ignore
    scriptCorpusObj[item.contentName] = item
  })

  const errMsgArr: string[] = []
  const params: {
    scriptId: number, // 话术ID
    corpusId: number, // 语料ID
    contentId: number, // 语句ID
    content: string, // 文字内容
  }[] = []
  data.forEach(item => {
    if (!item['语料名称']) {
      return errMsgArr.push('缺少语料名称')
    }
    if (item['语料名称'] && typeof scriptCorpusObj[item['语料名称']]?.id !== 'number') {
      return errMsgArr.push(`${item['语料名称']}：未找到匹配的语料`)
    }
    if (!item['文字内容']) {
      return errMsgArr.push(`${item['语料名称']}：缺少文字内容`)
    }
    params.push({
      scriptId,
      corpusId: scriptCorpusObj[item['语料名称']]?.corpusId!,
      contentId: scriptCorpusObj[item['语料名称']]?.id!,
      content: item['文字内容'],
    })
  })
  if (errMsgArr.length > 0) {
    console.log(errMsgArr)
    ElMessage({
      type: 'warning',
      message: errMsgArr.join('、')
    })
    return null
  }
  return params
}
