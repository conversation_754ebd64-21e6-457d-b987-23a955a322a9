import { CorpusTypeEnum, AudioStatusEnum, AudioItem, AsrStatusEnum, InterruptHangUpTypeEnum, InterruptTypeEnum } from '@/type/speech-craft'
import { findValueInEnum, } from '@/utils/utils'


// 用于音频组件的数据类型，区别于列表数据，注意转换
export interface AudioInfoItem {
  id?: number,
  name?: string | null // 语料名称
  content?: string | null// 文字内容
  audioPath?: string | null // 音频文件URL
  audioStatus?: AudioStatusEnum,
  audioTag?: string,
}

export class TempAudioOrigin implements AudioInfoItem {
  id = -1
  contentName = ''
  content = ''
  corpusId = -1
  corpusType = CorpusTypeEnum['主动流程-普通语料']
  audioPath = ''
  audioStatus = AudioStatusEnum['未验听']
  audioTag = ''
}

export const corpusTypeMixOption = [
  {
    name: '主动流程',
    value: [CorpusTypeEnum['主动流程-普通语料'], CorpusTypeEnum['主动流程-连接语料']].join(',')
  },
  {
    name: '知识库',
    value: [CorpusTypeEnum['深层沟通-普通语料'], CorpusTypeEnum['深层沟通-连接语料'], CorpusTypeEnum['基本问答']].join(',')
  },
  {
    name: '功能话术',
    value: [
      CorpusTypeEnum['最高优先'], CorpusTypeEnum['重复语料'], CorpusTypeEnum['沉默语料'],
      CorpusTypeEnum['打断垫句'], CorpusTypeEnum['续播垫句'], CorpusTypeEnum['承接语料'],
    ].join(',')
  },
]

export const markShortcuts: string[] = [
  '音文不符', '音色不符', '主体不符', '音频缺句', '音频丢字',
  '音频错字', '发音不清', '音质问题', '剪辑问题',
  '文本错字', '文本缺句', '文本丢字',
]

export const audioStatusColorMap: Record<AudioStatusEnum, string> = {
  [AudioStatusEnum['已验听']]: 'green-status',
  [AudioStatusEnum['未验听']]: 'red-status',
  [AudioStatusEnum['已标记']]: 'orange-status',
}

export const translateCorpusContentInterrupt = (row: AudioItem) => {
  if (!row.corpusType || !row.interruptType || row.corpusType.includes('PRE_')) {
    return '-'
  }
  if (row.interruptType === InterruptTypeEnum['不允许打断']) {
    return '不允许打断'
  }
  return `${row.allowedInterruptTime}秒后,
    ${findValueInEnum(row.interruptType, InterruptTypeEnum)}
  `
  // 打断时间 + 打断类型
  // row.preInterruptCorpusId
  // row.preContinueCorpusIdForInterrupt
  // // 挂机语料：指定语料
  // row.interruptCorpusIdsForEnd
}

/** 格式化音文相似度样式名 */
export const formatSimilarClass = (row: AudioItem) => {
  if (typeof row.asrResult === 'number') {
    return (row.asrResult >= 95 ? 'green-status' : row.asrResult >= 90 ? 'orange-status' : 'red-status')
  } else {
    return 'gray-status'
  }
}


/** 格式化音文相似度状态和百分比 */
export const formatSimilarPercent = (row: AudioItem) => {
  // 没有识别完成就展示状态，识别完成就展示相似度百分比
  if (row?.asrStatus !== AsrStatusEnum['已完成']) {
    return findValueInEnum(row.asrStatus, AsrStatusEnum) || '-'
  } else {
    return typeof row?.asrResult === 'number' ? (row?.asrResult + '%') : '-'
  }
}
