<template>
  <!--产品名称置顶不移动-->
  <div v-show="currentProduct.productDisplayName" class="tw-flex-none tw-px-[16px] tw-py-[8px]" style="background-color: var(--primary-black-color-50);">
    <span class="info-title info-text-bold">
      {{ currentProduct.occurrenceTime ?? '-' }}&nbsp;&nbsp;
    </span>
    <span class="info-content info-text-bold">进入产品：</span>
    <br>
    <span class="info-content info-text-bold" v-html="getProductText(currentProduct.productDisplayName ?? '-')" />
  </div>

  <el-scrollbar ref="scrollRef" view-class="tw-flex-auto tw-min-h-[1px] tw-py-[16px] tw-px-[16px]">
    <!--空数据-->
    <div v-show="!eventList.length">
      暂无数据
    </div>
    <!--滚动列表-->
    <div v-show="eventList.length" ref="contentRef">
      <!--日期时间+动作事件+进入产品-->
      <!--最后（最新）一条加粗-->
      <!--产品名称彩色-->
      <p
        v-for="(clientStatusItem, clientStatusIndex) in eventList"
        :key="`${clientStatusItem.occurrenceTime}-${clientStatusIndex}`"
        class="info-item"
        :class="{'info-text-bold': clientStatusIndex >= eventList.length - 1}"
      >

        <!--日期时间-->
        <span class="info-title">
          {{ clientStatusItem.occurrenceTime ?? '-' }}&nbsp;&nbsp;
        </span>

        <!--事件-->
        <span v-show="clientStatusItem.stageString" class="info-content">
          {{ clientStatusItem.stageString ?? '-' }}
        </span>

        <!--连接前后的逗号-->
        <span v-show="clientStatusItem.stageString && clientStatusItem.productDisplayName">
          ，
        </span>

        <!--产品-->
        <template v-if="clientStatusItem.productDisplayName">
          <span>进入产品：</span>
          <br>
          <span v-html="getProductText(clientStatusItem.productDisplayName ?? '')" />
        </template>

      </p>
    </div>
  </el-scrollbar>
</template>

<script setup lang="ts">
import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import { useSeatPhoneStore } from '@/store/seat-phone'
import { storeToRefs } from 'pinia'
import { EventInfo } from '@/type/seat'

// ---------------------------------------- 通用 开始 ----------------------------------------

const seatPhoneStore = useSeatPhoneStore()
const { eventList } = storeToRefs(seatPhoneStore)

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 客户状态记录 开始 ----------------------------------------

const scrollRef = ref()
// 内容DOM
const contentRef = ref()
// 当前进入的产品
const currentProduct = ref<EventInfo>({})

// 客户状态记录发生变化
watch(eventList, async (newEventList) => {
  if (scrollRef.value && contentRef.value) {
    // 取客户状态列表里最新一条有效产品，作为当前产品
    let hasProduct = false
    for (let i = newEventList.length - 1; i >= 0; i--) {
      const item = newEventList[i] ?? {}
      if (item?.productDisplayName) {
        currentProduct.value = JSON.parse(JSON.stringify(item))
        hasProduct = true
        break
      }
    }
    if (!hasProduct) {
      currentProduct.value = {}
    }
    // 等DOM更新完内容，防止高度不对
    await nextTick()
    // 滚动到底部
    const contentHeight = contentRef.value.scrollHeight
    scrollRef.value.setScrollTop(contentHeight)
  }
}, {
  deep: true,
  immediate: true,
})

/**
 * 获取产品展示文本
 * @param str 原始字符串
 */
const getProductText = (str: string = '') => {
  // 括号左边是蓝色，括号本身、括号内和括号右边是黑色
  const arr = str?.split('') ?? []
  const index = arr.findIndex((char: string) => {
    return char === '(' || char === '（'
  })
  // 找到左括号的索引位置
  if (index > -1) {
    // 蓝色文本
    const name = str.substring(0, index)
    // 普通文本
    let desc = str.substring(index)
    // 拼接字符串，返回HTML
    return `<span style="color: var(--primary-blue-color);">${name}</span>
          <span>${desc}</span>`
  } else {
    return `<span style="color: var(--primary-blue-color);">${str}</span>`
  }
}

onMounted(() => {
  currentProduct.value = {}
})
onUnmounted(() => {
  currentProduct.value = {}
})

// ---------------------------------------- 客户状态记录 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
.info-item {
  margin: 0 0 12px;
}
.info-text-bold {
  font-weight: bold;
}
</style>
