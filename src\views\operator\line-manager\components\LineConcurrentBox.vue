<template>
  <div class="tw-flex tw-pb-[6px]">
    <el-radio-group v-model="date" @change="handleDateChange">
      <el-radio-button :label="dayjs().format('YYYY-MM-DD')">今日</el-radio-button>
      <el-radio-button :label="dayjs().add(-1, 'days').format('YYYY-MM-DD')">昨日</el-radio-button>
      <el-radio-button :label="dayjs().add(-2, 'days').format('YYYY-MM-DD')">前日</el-radio-button>
    </el-radio-group>
  </div>
  <div v-loading="loading1" class="tw-bg-white tw-py-[12px] tw-w-full">
    <div class="form-dialog-header tw-flex tw-px-[16px] tw-items-center">
      <div>并发趋势</div>
      <el-tooltip content="刷新" placement="right" :show-after="500">
        <el-icon size="13" color="var(--el-color-primary)" class="tw-cursor-pointer tw-ml-0.5" @click="search()">
          <SvgIcon name="reset" color="inherit"/>
        </el-icon>
      </el-tooltip>
    </div>
    
    <LineChart
      :data="concurrentData || []"
      :yInfo="[{
        propName: 'value1',
        name: '并发量',
      }]"
      :xInfo="{
        propName: 'name',
        name: '时间',
      }"
      yName="并发量"
    ></LineChart>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch, } from 'vue'
import LineChart from '@/components/charts/LineChart2.vue'
import { lineChartModel, } from '@/api/line'
import to from 'await-to-js'
import dayjs from 'dayjs'
import { onBeforeRouteLeave } from 'vue-router'
import { RegionChartItem } from '@/type/task'

const loading1 = ref(false) // 分时段统计
const props = defineProps<{
  lineInfo: string,
  refresh: boolean
}>()
const emits = defineEmits([
  'update:refresh',
])

const concurrentData = ref<RegionChartItem[] | null>([])
// 初始化所有图数据
const date = ref<string>(dayjs().format('YYYY-MM-DD'))
const handleDateChange = () => {
  search()
}
const search = async () =>{
  loading1.value = true
  const [_, data] = await to(lineChartModel.getConcurrentByDate({
    key: props.lineInfo,
    date: date.value
  }))
  concurrentData.value = []
  if (data) {
    const keys = Object.keys(data).sort((a,b) => dayjs(a).isAfter(dayjs(b)) ? 1 : -1) || []
    concurrentData.value = keys.map(item => {
      return {
        name: item ? dayjs(item).format('HH:mm') : '',
        value1: data[item] || 0,
      }
    })
  }
  loading1.value = false
  emits('update:refresh', false)
}

watch(() => props.refresh, () => {
  props.refresh && !!props.lineInfo && search()
}, {immediate: true})

onBeforeRouteLeave(() => {
  concurrentData.value = null
})
</script>

<style scoped lang="postcss" type="text/postcss">
.line-chart-box {
  :deep(.el-radio-button__inner) {
    border: 0;
  }
  .button-box {
    padding: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-around;
    background-color: #fff;
    border-radius: 8px;
    padding: 16px 12px;
    position: relative;
    &.active {
      background: linear-gradient(180deg, #0167FF 0%, #019FFF 100%);;
      color: #fff;
      &::after {
        content: "";
        position: absolute;
        background: inherit;
        z-index: 0;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        width:0;
        height: 0;
        border-top: 6px solid #019FFF;
		    border-left: 6px solid transparent;
		    border-right: 6px solid transparent;
      }
    }
  }
}
</style>