<template>
  
  <div v-if="props.audioUrl" v-loading="loading" class="audio-box" @mouseup="handleDragEnd">
    <el-icon class="tw-cursor-pointer" :size="16" @click="handlePause" color="#323233">
      <SvgIcon v-if="['pause', 'none'].includes(isPaused)" name="play-fill"/>
      <SvgIcon v-else name="stop-fill"/>
    </el-icon>
    <div class="tw-w-1/2 tw-h-full tw-flex tw-items-center" @mousedown="handleDragStart">
      <el-slider
        v-model="currentTime"
        :step="0.1"
        :show-tooltip="false"
        :max="tempDuration"
        size="small"
        @input="handleTimeChange"
      />
    </div>
    <div class="tw-text-[#323233] tw-min-w-[60px] tw-flex tw-justify-around">
      <span class="tw-text-left">{{ Math.round(currentTime*10)/10 || 0 }}</span>
      <span>/</span>
      <span>{{ tempDuration.toFixed(1) || 0 }}</span>
    </div>
    <el-tooltip
      trigger="click"
      effect="light"
    >
      <el-icon class="tw-cursor-pointer" :size="16" color="#323233"><SvgIcon name="laba"/></el-icon>
      <template #content>
        <div class="tw-w-[50px] tw-py-1">
          <el-slider class="voice-slider" v-model="audioVolume" size="small" vertical height="120px" :min="0" :max="100" :marks="marks" @change="handleVolumeChange"/>
          <div v-if="audioVolume <= 95 && audioVolume >= 5" class="tw-absolute tw-left-[44px] tw-text-[#909399] tw-text-[13px]" :style="{'bottom': (1.2 * audioVolume + 10) + 'px'}">{{ audioVolume }}</div>
        </div>
      </template>
    </el-tooltip>
    <el-icon class="tw-cursor-pointer" :size="16" @click="download" color="#323233"><SvgIcon name="download2" /></el-icon>
    <el-icon class="tw-cursor-pointer" :size="16" @click="copyUrl" color="#323233"><SvgIcon name="link" /></el-icon>
    <el-dropdown  @command="changeRate" class="tw-cursor-pointer tw-text-[#323233]">
      <span class="tw-cursor-pointer">{{ audioRateOption.find(item => item.value==tempRate)?.name || audioRateOption[0]!.name }}</span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item v-for="item in audioRateOption" :command="item.value" :key="item.value">
            <span v-if="tempRate===item.value" class="tw-text-[#409eff] tw-flex tw-items-center">{{ item.name }}<el-icon color="#409eff" class="tw-ml-0.5"><Select /></el-icon></span>
            <span v-else>{{ item.name }}</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <el-icon v-if="closeable" class="tw-cursor-pointer" :size="16" color="#323233" @click="closeAudio"><SvgIcon name="close" color="#323233" /></el-icon>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus';
import { Select } from '@element-plus/icons-vue'
import { ref, onMounted, watch, onActivated, onDeactivated, onUnmounted } from 'vue'
import { onBeforeRouteLeave } from 'vue-router';
import { changeAudioUrlOrigin, copyText } from '@/utils/utils'
import { audioRateOption } from '@/assets/js/constant';
import { trace } from '@/utils/trace'

const props = defineProps<{
  audioUrl: string;
  audioName: string;
  audioStatus: 'pause' | 'play' | 'none';
  audioVolume: number
  closeable?: boolean
  keepOrigin?: boolean
}>();
const emits = defineEmits(['close', 'update:audioStatus', 'update:audioVolume'])
const closeable = props.closeable || false
const tempRate = ref(1)
const audioVolume = ref(props.audioVolume)
const marks = {
  0: '0',
  100: '100'
}

const tempDuration = ref<number>(0)
const currentTime = ref<number>(0)
const isPaused = ref<'pause' | 'play' | 'none' | 'drag'>('none')
const audioRef = ref<HTMLAudioElement|null>(new Audio())
const loading = ref(false)
onMounted(() => {
  if (!audioRef.value) {
    audioRef.value = new Audio()
  }
  if (props.audioUrl) {
    audioRef.value.src = (!!props.keepOrigin ? props.audioUrl : changeAudioUrlOrigin(props.audioUrl))  + '?rand='+Math.random()
  }
  audioRef.value?.addEventListener('loadstart', () => {
    loading.value = true
    // 加载超过5秒默认清loading
    setTimeout(() => {
      loading.value = false
    }, 5000)
  })
  audioRef.value?.addEventListener('loadedmetadata', () => {
    const r = audioRef.value?.duration || 0
    tempDuration.value =  Math.round(r*10)/10
    currentTime.value = 0
    loading.value = false
    isPaused.value = props.audioStatus
    props.audioStatus === 'play' && audioRef.value?.play() 
  })
  audioRef.value?.addEventListener('timeupdate', () => {
    const cur = audioRef.value?.currentTime
    if (cur) {
      currentTime.value = Math.round(cur * 10)/10
    } else {
      currentTime.value = 0
    }
  })
  audioRef.value?.addEventListener('pause', () => {
    if (isPaused.value !== 'drag') {
      isPaused.value = 'pause'
      emits('update:audioStatus', 'pause')
    }
  })
  audioRef.value?.addEventListener('play', () => {
    if (isPaused.value !== 'drag'){
      isPaused.value = 'play'
      emits('update:audioStatus', 'play')
    }
  })
  // audioRef.value?.addEventListener('ended', () => {
  //   if (isPaused.value !== 'pause'){
  //     isPaused.value = 'pause'
  //     emits('update:audioStatus', 'pause')
  //   }
  // })
})
onDeactivated(() => {
  closeAudio()
})
onActivated(() => {
  audioRef.value && audioRef.value.pause()
  if(!audioRef.value) {
    audioRef.value = new Audio()
    audioRef.value.src = (!!props.keepOrigin ? props.audioUrl : changeAudioUrlOrigin(props.audioUrl)) + '?rand='+Math.random()
  }
})
onUnmounted(() => {
  if (audioRef.value) {
    audioRef.value.pause()
    audioRef.value.src = ''
  }
  audioRef.value = null
})

watch(() => props.audioVolume, n => {
  audioVolume.value = n < 0 ? 0 : (n > 100 ? 100 : n)
  if (audioRef.value) {
    audioRef.value.volume = n / 100
  }
})
watch([() => props.audioUrl, () => props.audioStatus], (n, o) => {
  // 没有链接立即停止播放
  if (!props.audioUrl) {
    audioRef.value?.pause()
    isPaused.value = 'pause'
    return
  }
  // 同一个链接，更新isPaused
  if (n[0] === o[0] && n[1] !== o[1]) {
    if (props.audioStatus === 'play') {
      audioRef.value?.play()
    } else {
      audioRef.value?.pause()
    }
  } else if (n[0] !== o[0]) {
    // 链接更新，更新状态，loadedmetadata后会自动根据状态播放
    !audioRef.value && (audioRef.value = new Audio())
    audioRef.value.src = (!!props.keepOrigin ? props.audioUrl : changeAudioUrlOrigin(props.audioUrl)) + '?rand='+Math.random()
    audioRef.value.playbackRate = tempRate.value
    currentTime.value = 0
  }
}, {deep: true})

// 时间拖拽函数
const handleTimeChange = (val: number) => {
  if (audioRef.value && isPaused.value !== 'drag') {
    audioRef.value && (audioRef.value.currentTime = Math.round((val??0) * 10)/10)
    if (isPaused.value !== 'play' && audioRef.value) {
      setTimeout(() => {
        if (isPaused.value !== 'play' && isPaused.value !== 'drag' && audioRef.value) {
          audioRef.value.play()
        }
      }, 100)
    }
  }
}
const handleDragStart = () => {
  isPaused.value = 'drag'
  audioRef.value && audioRef.value.pause()
}
const timer = ref<ReturnType<typeof setTimeout> | null>(null)
const handleDragEnd = () => {
  if ( isPaused.value === 'drag') {
    if (timer.value) clearTimeout(timer.value)
    timer.value = setTimeout(() => {
      isPaused.value = 'play'
      if (audioRef.value) {
        audioRef.value.currentTime = Math.round((currentTime.value??0) * 10)/10
        audioRef.value.play()
      }
      timer.value = null
    }, 0)
  }
}

const handlePause = () => {
  if (audioRef.value && audioRef.value.duration>0) {
    if(audioRef.value?.paused ) {
      isPaused.value = 'play'
      audioRef.value && audioRef.value.play()
     } else {
      audioRef.value && audioRef.value.pause()
     }
  } else {
    ElMessage({
      type: 'warning',
      message: '音频链接为空或音频时长为空！'
    })
  }
}
const handleVolumeChange = (val: number) => {
  if (audioRef.value) {
    audioRef.value.volume = val / 100
    emits('update:audioVolume', val)
  }
}
const changeRate = (val: number) => {
  if (audioRef.value) {
    audioRef.value.playbackRate = val
    tempRate.value = val
  }
}
const closeAudio = () => {
  if (audioRef.value) {
    audioRef.value.src = ''
  }
  audioRef.value = null
  emits('close')
}
const download = async () => {
  if (audioRef.value && audioRef.value.src && props.audioName) {
    trace({
      page: `通用-音频组件-下载音频`,
      params: {
        props,
        url: location.href
      }
    })
    const link = document.createElement('a');
    link.href = audioRef.value.src.split('?')[0] || ''
    link.download = props.audioName + '.wav';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } else {
    ElMessage.error('获取下载资源错误')
  }
}
const copyUrl = () => {
  const url = audioRef.value?.src?.split('?')[0] || ''
  url && copyText(url)
  trace({
    page: `通用-音频组件-复制音频地址`,
    params: {
      props,
      url: location.href
    }
  })
}

const clearAll = () => {
  timer.value && clearTimeout(timer.value)
  timer.value = null
  audioRef.value = null
}
onUnmounted(() => {
  clearAll()
})
onBeforeRouteLeave(() => {
  clearAll()
})
</script>

<style lang="postcss" type="text/postcss" scoped>
.audio-box {
  background-color: #F0F0F0;
  border-radius:4px;
  width: 483px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 40px;
  line-height: 20px;
  .audio-icon {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #eee;
    border-radius: 0 0 27px 27px;
    border-radius: 50%;
    line-height: 24px;
    width: 24px;
    height: 24px;
    display: none;
  }
  .el-slider--small {
    height: 15px;
    :deep(.el-slider__button-wrapper) {
      top: -4.5px;
      height: 16px;
      width: 10px;
      display: flex;
      align-items: center;
    }
    :deep(.el-slider__runway) {
      height: 7px;
      box-sizing: content-box;
      border-top: 15px solid transparent;
      border-bottom: 15px solid transparent;
      background-clip: padding-box;
    }
    :deep(.el-slider__bar) {
      height: 7px;
    }
  }
  :deep(.el-slider__button) {
    width: 8px;
    box-sizing: border-box;
    height: 8px;
    border: none;
  }
}
.voice-slider {
  :deep(.el-slider__marks-text) {
    font-size: 13px;
    left: 10px;
  }
  :deep(.el-slider__stop) {
    display: none;
  }
  :deep(.el-slider__button) {
    width: 12px;
    box-sizing: border-box;
    height: 12px;
  }
}
</style>
