import Paragraph from '@tiptap/extension-paragraph'
import { TextSelection } from 'prosemirror-state'
import { CorpusTypeEnum, AudioStatusEnum } from '@/type/speech-craft'

export default Paragraph.extend({
  addAttributes() {
    return {
      contentId: {
        default: undefined,
      },
      audioStatus: {
        default: AudioStatusEnum['未验听'],
      },
      deleted: {
        default: false,
      },
      corpusType: {
        type: CorpusTypeEnum,
      }
    }
  },
  // 回车新增的节点不继承上一个p属性
  addKeyboardShortcuts() {
    return {
      'Enter': ({ editor, }) => {
        const { state, dispatch } = editor.view
        const { $from, $to } = state.selection
        if ($from.parent.type !== this.type) return false

        const pos = $to.end($to.depth) + 1
        const tr = state.tr.insert(pos, this.type.createAndFill()!)
        tr.setSelection(TextSelection.near(tr.doc.resolve(pos)))
        dispatch(tr)
        return true
      },
      'Backspace': ({ editor }) => {
        const { state, dispatch } = editor.view
        const { $from, $to, empty } = state.selection

        // 检查是否在段落的结束位置，不存在选中内容
        if ($from.parentOffset < 1 && $to.parentOffset < 1) {
          // 判断当前段落是否有contentId，没有的话删除该段落，但可能删除上一段原因必须手动delete
          if (!$from.parent.attrs?.contentId) {
            const tr = state.tr;
            tr.delete($from.pos > 0 ? $from.pos - 1 : 0, $to.pos);
            dispatch(tr)
          }
          return true
        }
        
        // 存在选中内容，仅清空段落内容
        if (!empty && $from.pos !== $to.pos) {
          const tr = state.tr;
          const modifiedList: {start: number, end: number}[] = [];
          state.doc.nodesBetween($from.pos, $to.pos, (node, pos) => {
            if (node.type.name === 'paragraph') {
              // 如果选区跨越了多个段落，清空这些段落内容但不删除段落节点
              if (!node.textContent.length) return;
              const start = Math.max(pos + 1, $from.pos);
              const end = Math.min(pos + node.nodeSize - 1, $to.pos);
              modifiedList.push({start, end})
            }
          });
          modifiedList?.reverse()?.forEach(item => {
            const start = Math.min(item.start, tr.doc.content.size);
            const end = Math.min(item.end, tr.doc.content.size);
            tr.delete(start, end);
          })
          dispatch(tr)
          return true;
        }
        // 其他情况下，让 Tiptap 处理默认行为
        return false
      }
    }
  },
})
