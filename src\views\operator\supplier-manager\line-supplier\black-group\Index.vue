<template>
  <div v-loading="loadingBlackAllList" class="tab-container">
    <div class="tab-header">
      <div class="tw-flex tw-justify-end">
        <el-button type="primary" @click="onClickAddBlackList">
          新增黑名单
        </el-button>
      </div>
    </div>
    <!--表格-->
    <el-table
      stripe
      :data="supplyCurrentBlacklist"
      :header-cell-style="tableHeaderStyle"
      class="tw-mt-[12px]"
    >
      <template #empty>
        暂无数据
      </template>

      <el-table-column align="left" prop="groupName" label="黑名单分组" min-width="100" show-overflow-tooltip />
      <el-table-column align="left" prop="supplyLines" label="生效范围" min-width="300" show-overflow-tooltip>
        <template v-slot="{row}">
          {{ formatBlackGroupScopeText(row) }}
        </template>
      </el-table-column>
      <el-table-column align="right" fixed="right" label="操作" width="100">
        <template v-slot="{row}">
          <el-button type="primary" link @click="onClickEditBlacklist(row)">
            编辑
          </el-button>
          <el-button type="danger" link @click="onClickDeleteBlacklist(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--分页条-->
    <PaginationBox
      v-if="supplyAllBlacklist?.length"
      :pageSize="blackPageSize"
      :currentPage="blackPageNum"
      :total="supplyAllBlacklist?.length ?? 0"
      @search="updateBlackAllList"
      @update="updateBlackCurrentList"
    />
  </div>

  <!--黑名单挂载弹窗-->
  <BlackGroupDialog
    v-model:visible="blackGroupDialogVisible"
    :data="blackGroupDialogData"
    :availableBlacklist="availableBlacklist"
    @confirm="updateBlackAllList"
  />
</template>

<script setup lang="ts">
import { tableHeaderStyle } from '@/assets/js/constant'
import { SupplierBlackGroupInfo, SupplierBlackGroupParam, SupplierLineInfo } from '@/type/supplier'
import { computed, defineAsyncComponent, ref, watch } from 'vue'
import { Throttle, updateCurrentPageList } from '@/utils/utils'
import to from 'await-to-js'
import { supplierModel } from '@/api/supplier'
import { ElMessage } from 'element-plus'
import Confirm from '@/components/message-box'
import { useSupplierStore } from '@/store/supplier'
import { formatBlackGroupScopeText } from './constant'
import { BlackListTypeEnum, BlackListGroupItem } from '@/type/dataFilter'
import { blacklistModel } from '@/api/data-filter'

// 动态引入组件
const BlackGroupDialog = defineAsyncComponent(() => import('./BlackGroupDialog.vue'))

// ---------------------------------------- 通用 开始 ----------------------------------------

const props = withDefaults(defineProps<{
  needUpdate: boolean,
}>(), {
  needUpdate: false,
})
const emits = defineEmits(['update'])

const supplierStore = useSupplierStore()

watch(() => props.needUpdate, (val) => {
  if (val) {
    updateBlackAllList()
  }
})

// ---------------------------------------- 通用 开始 ----------------------------------------

// ---------------------------------------- 黑名单分组 开始 ----------------------------------------

// 黑名单分组列表，全部，接口数据
const supplyAllBlacklist = ref<SupplierBlackGroupInfo[]>([])
// 黑名单分组列表，全部，正在加载
const loadingBlackAllList = ref<boolean>(false)
// 黑名单分组列表，全部，加载节流锁
const throttleBlackAllList = new Throttle(loadingBlackAllList)

// 黑名单分组列表，当前页，页面展示
const supplyCurrentBlacklist = computed(() => {
  return supplyAllBlacklist.value.slice((blackPageNum.value - 1) * blackPageSize.value, blackPageNum.value * blackPageSize.value)
})

// 黑名单分组列表，当前页码，从1开始
const blackPageNum = ref<number>(1)
// 黑名单分组列表，每页大小
const blackPageSize = ref<number>(20)

/**
 * 黑名单分组列表，更新全部
 */
const updateBlackAllList = async () => {
  // 节流锁上锁
  if (throttleBlackAllList.check()) {
    return
  }
  throttleBlackAllList.lock()

  // 处理参数
  const params: SupplierBlackGroupParam = {
    callLineSupplierId: supplierStore.currentSupplier.id ?? undefined
  }
  // 请求接口
  const [err, res] = <[any, SupplierBlackGroupInfo[]]>await to(supplierModel.getBlacklist(params))

  // 更新列表
  supplyAllBlacklist.value = res?.length ? res : []

  emits('update')

  // 节流锁解锁
  throttleBlackAllList.unlock()
}

/** 黑名单分组列表，更新当前页*/
const updateBlackCurrentList = (page: number, size: number) => {
  blackPageNum.value = page || 1
  blackPageSize.value = size || 20
}

/**
 * 黑名单分组列表，点击删除按钮
 * @param {SupplierBlackGroupInfo} row 当前黑名单分组
 */
const onClickDeleteBlacklist = (row: SupplierBlackGroupInfo) => {
  const name = row?.groupName ?? ''
  // 显示确认弹窗
  Confirm({
    text: `你确定要删除黑名单分组【${name}】吗？`,
    type: 'danger',
    title: `删除确认`,
  }).then(async () => {
    // 处理参数
    const params: SupplierBlackGroupParam = {
      callLineSupplierId: supplierStore.currentSupplier.id ?? undefined,
      blackListGroupId: row?.id ?? undefined,
    }
    // 请求接口
    const [err] = await to(supplierModel.deleteBlacklist(params))
    if (!err) {
      ElMessage.success(`成功删除黑名单分组【${name}】`)
    }
  }).catch(() => {
  }).finally(() => {
    // 更新列表
    updateBlackAllList()
  })
}


// ---------------------------------------- 黑名单分组 结束 ----------------------------------------

// ---------------------------------------- 黑名单挂载弹窗 开始 ----------------------------------------

// 黑名单挂载弹窗 显示
const blackGroupDialogVisible = ref<boolean>(false)
// 黑名单挂载弹窗 数据
const blackGroupDialogData = ref<SupplierBlackGroupInfo | null>(null)
// 可选择的黑名单
const availableBlacklist = ref<BlackListGroupItem[] | null>([])

/** 黑名单分组列表，点击新增按钮 */
const onClickAddBlackList = async () => {
  await updateAvailableBlacklist()
  blackGroupDialogData.value = null
  blackGroupDialogVisible.value = true
}

/** 黑名单分组列表，点击编辑按钮 */
const onClickEditBlacklist = async (row: SupplierBlackGroupInfo) => {
  if (!row) return ElMessage.error('黑名单分组信息获取失败')
  await updateAvailableBlacklist(row.id)
  blackGroupDialogData.value = row ?? null
  blackGroupDialogVisible.value = true
}

const updateAvailableBlacklist = async (id?: number) => {
  loadingBlackAllList.value = true
  // 处理参数
  const params = { groupType: BlackListTypeEnum['供应商'] }
  // 请求接口
  const [err, res] = await to(blacklistModel.getGroupList(params))

  // 需要剔除当前供应商已配置的黑名单分组
  const ids = supplyAllBlacklist.value?.map(item => item.id) || []
  availableBlacklist.value = (res || []).filter((item: SupplierBlackGroupInfo) => {
    return item.id && (!ids.includes(item.id) || item.id === id)
  })
  loadingBlackAllList.value = false
}

// ---------------------------------------- 黑名单挂载弹窗 结束 ----------------------------------------


// ---------------------------------------- 立即执行 开始 ----------------------------------------

updateBlackAllList()

// ---------------------------------------- 立即执行 结束 ----------------------------------------

</script>

<style scoped lang="postcss">
/* 标签卡 */
.tab-container {
  padding: 0;
  text-align: left;
  /* 标签卡顶部 */
  .tab-header {
    padding: 12px 16px 0;
  }
}
</style>
