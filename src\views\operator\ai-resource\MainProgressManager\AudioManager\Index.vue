<template>
  <div class="audio-container">
    <div class="tw-mb-[8px] tw-text-left">
      特别注意：音频文件上传必须为8000Hz采样率，16bit，单声道PCM，wav格式，名称为语料名称
    </div>
    <div class="tw-mb-[8px] tw-flex tw-flex-col">
      <div class="tw-grid tw-grid-cols-7 tw-gap-1">
        <el-input
          v-model.trim="searchForm.content"
          clearable
          placeholder="文字内容（300字以内）"
          maxlength="300"
          @clear="initData(true)"
          @keyup.enter="initData(true)"
        >
        </el-input>
        <el-input
          v-model.trim="searchForm.unitContentName"
          clearable
          placeholder="语料名称（20字以内）"
          maxlength="20"
          @clear="initData(true)"
          @keyup.enter="initData(true)"
        >
        </el-input>
        <el-input
          v-model.trim="searchForm.canvasName"
          clearable
          placeholder="所属流程（20字以内）"
          maxlength="20"
          @clear="initData(true)"
          @keyup.enter="initData(true)"
        >
        </el-input>
        <el-select v-model="typeList" placeholder="请选语料类型" clearable @change="initData(true)">
          <el-option
            v-for="item in corpusTypeMixOption"
            :key="item.name"
            :value="item.value"
            :label="item.name"
          />
        </el-select>
        <el-select v-model="searchForm.audioStatus" placeholder="请选验听状态" clearable @change="initData(true)">
          <el-option v-for="item in enum2Options(AudioStatusEnum)" :key="item.value" :value="item.value" :label="item.name" />
        </el-select>
        <el-select v-model="searchForm.uploadStatus" placeholder="请选上传状态" clearable @change="initData(true)">
          <el-option value="已上传" label="已上传" />
          <el-option value="未上传" label="未上传" />
        </el-select>
        <div class="tw-flex tw-justify-start">
          <el-button type="primary" link @click="initData(true)">
            <el-icon :size="16">
              <SvgIcon name="search" color="none"></SvgIcon>
            </el-icon>
            <span>查询</span>
          </el-button>
        </div>
      </div>
      <div class="tw-flex tw-justify-between tw-mt-[8px]">
        <span class="tw-leading-[30px] tw-h-[30px]">{{ tip }}</span>
        <div class="tw-flex tw-items-center">
          <el-upload
            v-if="!isChecked"
            v-model:file-list="fileList"
            class="tw-mx-1"
            :action="actionUrl"
            :headers="headerInfo"
            multiple
            accept=".wav"
            :http-request="uploadFileAction"
          >
            <el-button type="primary">
              <el-icon :size="16" class="tw-mr-0.5">
                <SvgIcon name="upload"></SvgIcon>
              </el-icon>
              批量上传
            </el-button>
          </el-upload>
          <input ref="fileRef" type="file" accept=".xls,.xlsx" class="batch-edit tw-hidden" @change="handleEditFileChange" />
          <el-button v-if="!isChecked" type="primary" @click="batchEdit">
            <el-icon :size="16" class="tw-mr-0.5">
              <SvgIcon name="edit2" color="none"></SvgIcon>
            </el-icon>
            批量编辑
          </el-button>
          <el-button v-if="canDownload" @click="export2Excel">
            <el-icon :size="16" class="tw-mr-0.5">
              <SvgIcon name="download3" color="none"></SvgIcon>
            </el-icon>
            导出表格
          </el-button>
          <el-button v-if="canDownloadAudio" :loading="downloadingAudio" @click="exportAllAudio">
            <el-icon :size="16" class="tw-mr-0.5">
              <SvgIcon name="download3" color="none"></SvgIcon>
            </el-icon>
            {{ downloadingAudio ? '正在下载所选音频' : '导出音频' }}
          </el-button>
          <el-button v-if="!isChecked" type="danger" @click="delAll">
            <el-icon :size="16" class="tw-mr-0.5" color="inherit">
              <SvgIcon name="delete"></SvgIcon>
            </el-icon>
            全部删除
          </el-button>
        </div>
      </div>
    </div>
    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="tableData"
      style="width: 100%"
      :row-style="getRowClass"
      class="tw-grow"
      row-key="id"
      :header-cell-style="tableHeaderStyle"
      scrollbar-always-on
      @sort-change="handleSortChange"
    >
      <el-table-column width="36" align="left" fixed="left" type="selection"></el-table-column>
      <el-table-column property="contentName" fixed="left" label="语料名称" align="left" width="160">
        <template #default="{ row }: { row: AudioItem }">
          <div class="tw-line-clamp-2" @dblclick="handleDeleteCorpus(row)">
            {{ row.contentName || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column property="content" align="left" label="文字内容" min-width="300">
        <template #default="{ row }: { row: AudioItem }">
          {{ row.content || '-' }}
        </template>
      </el-table-column>
      <el-table-column property="contentType" label="文本类型" align="center" width="90">
        <template #default="{ row }: { row: AudioItem }">
          {{ findValueInEnum(row.contentType, ContentTypeEnum) || '普通文本' }}
        </template>
      </el-table-column>
      <el-table-column property="corpusType" label="语料类型" align="left" width="140">
        <template #default="{ row }: { row: AudioItem }">
          {{ row.corpusType ? corpusTypeOption[row.corpusType as CorpusTypeEnum]?.name : '-' }}
        </template>
      </el-table-column>
      <el-table-column property="canvasName" align="left" label="所属流程" min-width="120" :formatter="formatterEmptyData"></el-table-column>
      <el-table-column property="interruptType" label="打断设置" align="left" width="230">
        <template #default="{ row }: { row: AudioItem }">
          <span>
            {{ translateCorpusContentInterrupt(row) }}
          </span>
          <el-button link v-if="row?.corpusId && !(row.corpusType?.includes('PRE_') || row.corpusType?.includes('_CONNECT'))" type="primary" @click="updateCorpusInterrupt(row)">
            <el-icon :size="16">
              <svg-icon name="edit2"></svg-icon>
            </el-icon>
          </el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" property="uploadStatus" label="上传状态" width="72">
        <template #default="{ row }: { row: AudioItem }">
          <span class="status-box-mini tw-mx-auto" :class="row.uploadStatus === '已上传' ? 'green-status' : 'red-status'">
            {{ row.uploadStatus === '已上传' ? '已上传' : '未上传' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" property="audioStatus" label="验听状态" width="90">
        <template #default="{ row }: { row: AudioItem }">
          <span v-if="row.audioStatus" class="status-box-mini tw-mx-auto" :class="audioStatusColorMap[row.audioStatus]">
            {{ findValueInEnum(row.audioStatus, AudioStatusEnum) }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column align="left" property="audioTag" label="验听标记" :formatter="formatterEmptyData" width="160"></el-table-column>
      <el-table-column align="center" label="音文相似度" property="asrResult" width="100" sortable="custom">
        <template #header="{ column }: { column: TableColumnCtx<AudioItem> }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{ column.label }}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'">
                <CaretTop />
              </el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'">
                <CaretBottom />
              </el-icon>
            </div>
          </div>
        </template>
        <template #default="{ row }: { row: AudioItem }">
          <span
            class="audio-text-similar status-box-mini tw-mx-auto tw-cursor-pointer tw-select-none"
            :class="formatSimilarClass(row)"
            @click="onClickSimilarPercent(row)"
          >
            {{ formatSimilarPercent(row) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column property="updateTime" align="center" label="最后更新时间" width="160" :formatter="formatTime" sortable="custom">
        <template #header="{ column }: { column: TableColumnCtx<AudioItem> }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{ column.label }}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'">
                <CaretTop />
              </el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'">
                <CaretBottom />
              </el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="uploadTime" align="center" label="最后上传时间" width="160" :formatter="formatTime" sortable="custom">
        <template #header="{ column }: { column: TableColumnCtx<AudioItem> }">
          <div class="tw-flex tw-items-center tw-justify-center">
            <span>{{ column.label }}</span>
            <div class="tw-flex tw-flex-col tw-justify-center tw-items-center">
              <el-icon :size="11" :color="column.order==='ascending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'">
                <CaretTop />
              </el-icon>
              <el-icon :size="11" class="tw-mt-[-5px]" :color="column.order==='descending' ? 'var(--el-color-primary)' : 'var(--primary-black-color-400)'">
                <CaretBottom />
              </el-icon>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="right" fixed="right" width="140">
        <template #default="{ row }: { row: AudioItem }">
          <div class="tw-flex tw-justify-end tw-items-center tw-h-[24px] tw-overflow-hidden">
            <el-button link :type="row?.id && !isChecked ? 'primary':'default'" :disabled="!row?.id || isChecked" @click="updateCorpusContent(row)">
              <el-icon :size="20">
                <svg-icon name="edit2"></svg-icon>
              </el-icon>
            </el-button>
            <el-button v-if="row?.id && tempAudio.id == row.id && audioPlayStatus == 'play'" link type="primary" :disabled="!row.audioPath" @click="handleAudioPlay(row)">
              <el-icon :size="20">
                <svg-icon name="stop-circle"></svg-icon>
              </el-icon>
            </el-button>
            <el-button v-else :type="row.audioPath ? 'primary':'default'" :disabled="!row.audioPath" link @click="handleAudioPlay(row)">
              <el-icon :size="20">
                <svg-icon name="play-circle"></svg-icon>
              </el-icon>
            </el-button>
            <el-upload
              class="tw-ml-[8px] tw-flex tw-items-center"
              :action="actionUrl"
              :disabled="isChecked"
              :headers="headerInfo"
              accept=".wav"
              :drag="!isChecked"
              :http-request="(opt: UploadRequestOptions) => uploadSingleFileAction(opt, row)"
            >
              <el-button :type="row?.id && !isChecked ? 'primary':'default'" :disabled="isChecked" link>
                <el-icon :size="20">
                  <SvgIcon name="upload"></SvgIcon>
                </el-icon>
              </el-button>
            </el-upload>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <el-empty v-if="!tableData || tableData.length < 1" description="暂无数据" />
      </template>
    </el-table>
    <PaginationBox
      class="tw-grow-0"
      :pageSize="pageSize"
      :currentPage="currentPage"
      :total="total"
      @search="initData()"
      @update="updatePage"
    />
  </div>
  <CorpusEditDialog
    v-model:visible="corpusEditVisible"
    :corpusData="editCorpus"
    @confirm="initData()"
    @play="updatePlayStatus"
  />
  
  <ProcessDialog
    v-model:visible="uploadProcessVisible"
    :successNumber="successNumber"
    title="音频上传结果"
    :totalNumber="fileList?.length||0"
    :processInfo="uploadProcessInfo"
    @update:visible="closeUpload"
  />
  <MiniAudio
    v-if="!!tempAudio?.audioPath"
    v-model:audioStatus="audioPlayStatus"
    v-model:playRate="playRate"
    :tempAudio="tempAudio"
    :markable="[SpeechCraftStatusEnum['编辑中'], SpeechCraftStatusEnum['已驳回']].includes(scriptStore.status)"
    @close="handleAudioPlay()"
    @update:audio="handleAudioChange"
    @update:audioTag="handleAudioTagChange"
    @clearAudioMark="clearAudioMark"
  >
  </MiniAudio>
  <SimilarDialog
    v-model:visible="similarDialogVisible"
    :data="similarDialogData"
  />
  <UploadConfirmDialog
    v-model:data="uploadConfirmAudioItem"
    v-model:visible="uploadConfirmDialogVisible"
    @play="updatePlayStatus"
  >
    <template #upload>
      <el-upload
        class="el-button tw-flex tw-items-center"
        style="padding: 0; border: none;"
        :action="actionUrl"
        :headers="headerInfo"
        :show-file-list="false"
        accept=".wav"
        :http-request="(opt: UploadRequestOptions) => uploadSingleFileAction(opt, uploadConfirmAudioItem)"
      >
        <el-button>
          重新上传
        </el-button>
      </el-upload>
    </template>
  </UploadConfirmDialog>

  <CorpusContentSettingDrawer
    v-model:visible="corpusContentVisible"
    :corpusId="corpusId!"
    @confirm="initData()"
  />
</template>

<script lang="ts" setup>
import {
  AudioItem,
  AudioItemUploadParams,
  AudioResponse,
  ContentTypeEnum,
  CorpusTypeEnum,
  corpusTypeOption,
  AudioStatusEnum,
  SpeechCraftStatusEnum,
} from '@/type/speech-craft'
import { scriptAudioModel, scriptCorpusModel } from '@/api/speech-craft'
import { CaretBottom, CaretTop, } from '@element-plus/icons-vue'
import { computed, onActivated, onDeactivated, reactive, ref } from 'vue'
import { onBeforeRouteLeave } from 'vue-router'
import { checkXlsContent } from '@/utils/script'
import { ElMessage, TableColumnCtx, UploadRequestOptions } from 'element-plus'
import { changeAudioUrlOrigin, findValueInEnum, formatTime, getToken, pickAttrFromObj, handleTableSort, formatterEmptyData, enum2Options, } from "@/utils/utils"
import { useScriptStore } from '@/store/script'
import Confirm from '@/components/message-box'
import MiniAudio from '@/components/MiniAudio.vue'
import to from 'await-to-js';
import { exportExcelMarked, exportFileByBlob } from '@/utils/export'
import dayjs from 'dayjs'
import CorpusEditDialog from './CorpusEditDialog.vue'
import ProcessDialog from '@/components/ProcessDialog.vue'
import { tableHeaderStyle } from '@/assets/js/constant'
import { useUserStore } from "@/store/user";
import routeMap from '@/router/asyncRoute/route-map'
import { TempAudioOrigin, AudioInfoItem, corpusTypeMixOption, audioStatusColorMap, translateCorpusContentInterrupt,
  formatSimilarPercent, formatSimilarClass,
 } from './constant';
import SimilarDialog from './SimilarDialog.vue'
import UploadConfirmDialog from './UploadConfirmDialog.vue';
import { trace } from '@/utils/trace'
import CorpusContentSettingDrawer from '@/components/corpus/CorpusContentSettingDrawer.vue'

const loading = ref(false)
const scriptStore = useScriptStore()
const editId = scriptStore.id
const isChecked = scriptStore.isChecked
const tip = ref<string>('')
// 用户权限获取
const userStore = useUserStore();
const canDownload = userStore.permissions[routeMap['话术制作']!.id]?.includes(routeMap['话术制作']!.permissions['导出表格']!)
const canDownloadAudio = userStore.permissions[routeMap['话术制作']!.id]?.includes(routeMap['话术制作']!.permissions['导出音频']!)

const headerInfo = {
  token: getToken(),
}

const actionUrl = computed(() => {
  let baseURL = location.protocol === 'https:'
    ? import.meta.env.VITE_API_ORIGIN_HTTPS
    : import.meta.env.VITE_API_ORIGIN_HTTP
  return baseURL + 'AiSpeech/scriptCorpus/import'
})

const typeList = ref<string | undefined>(undefined)
const searchForm = reactive<{
  content: string,
  audioStatus?: AudioStatusEnum,
  canvasName?: string
  unitContentName: string,
  typeList?: CorpusTypeEnum[],
  uploadStatus?: string
  scriptId: number
}>({
  content: '',
  canvasName: '',
  unitContentName: '',
  audioStatus: undefined,
  uploadStatus: undefined,
  typeList: undefined,
  scriptId: editId,
})
// 表格区
const tableAllData = ref<AudioItem[] | null>([])
const tableData = ref<AudioItem[] | null>([])
const tableRef = ref()

// 分页部分
const currentPage = ref(1)
const total = ref(0)
const pageSize = ref(100)

const updatePage = (p: number, s: number) => {
  currentPage.value = p
  pageSize.value = s
  tableData.value = tableAllData.value?.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value) || []
}

// 搜索区
const initData = async (scrollTop:boolean = false) => {
  loading.value = true
  const tempId = tempAudio.id
  const tempAudioStatus = audioPlayStatus.value
  handleAudioPlay()
  const [_, res] = await to(scriptAudioModel.findAudioList({
    ...searchForm,
    typeList: typeList.value ? typeList.value.split(',') as CorpusTypeEnum[] : undefined
  })) as [any, AudioResponse]
  const { scriptUnitContents, totalNums = 0, uploadNums = 0 } = res || {
    scriptUnitContents: [],
    totalNums: 0,
    uploadNums: 0,
  }
  tip.value = `当前音频上传个数${uploadNums}/${totalNums}`
  // 缓存全部音频列表,按语料名称升序排序
  tableAllData.value = scriptUnitContents?.length ? scriptUnitContents?.sort((a, b) => {
    return a.contentName?.localeCompare(b.contentName ?? '') ?? 0
  }) : []
  // 如果是生产环境，需要替换音频地址
  if (import.meta.env.MODE.includes('production') && location.protocol === 'https:') {
    tableAllData.value.forEach((item: AudioItem) => {
      try {
        item.audioPath = changeAudioUrlOrigin(item.audioPath)
      } catch (e) {
      }
    })
  }
  total.value = tableAllData.value.length || 0
  // 根据当前排序，更新排序和分页后的tableData
  handleSortChange()

  if (tempId && tempId > 0) {
    const row = tableData.value?.find(item => item.id === tempId)
    tempAudioStatus === 'play' && handleAudioPlay(row)
  }
  scrollTop && tableRef.value?.setScrollTop(0)
  loading.value = false
}

// 列表排序
const orderCol = ref('')
const orderType = ref('')
const handleSortChange = async (params?: { prop: string, order: string }) => {
  if (params) {
    orderCol.value = params.prop
    orderType.value = params.order
  }
  const data = handleTableSort(tableAllData.value || [], orderCol.value, orderType.value)
  tableData.value = data.slice((currentPage.value - 1) * pageSize.value, currentPage.value * pageSize.value) || []
}
// 操作区-上传音频
const uploadSingleFileAction = async (opt: UploadRequestOptions, row: AudioItem | null) => {
  if (!opt.file.name.endsWith('.wav')) return ElMessage.error('上传音频类型必须为wav!')
  loading.value = true
  // 暂时关闭上传确认弹窗，防止用户误触
  uploadConfirmDialogVisible.value = false
  const file = new FormData()
  // 上传的文件名和语料名称不一致，标记一下，等上传成功后需要验听
  const same = opt.file.name.slice(0, -4) === row?.contentName
  // 文件名改为对应的语料名称
  file.append('file', opt.file, row?.contentName + '.wav')
  if (typeof row?.id === 'number') {
    const params: AudioItemUploadParams = {
      contentId: row.id,
      file,
    }
    trace({ page: `话术编辑-音频管理-单个上传-${same ? '同名' : '非同名'}(${editId})`, params: opt.file?.name })
    const [err, res] = <[any, string]>await to(scriptAudioModel.uploadSingleAudio(params))
    if (err) {
      ElMessage.error(`${opt.file.name || ''}文件上传失败`)
    } else {
      if (res?.includes('成功')) {
        ElMessage.success(`${opt.file.name || ''}文件上传成功`)
        const [_, data] = await to(scriptAudioModel.findAudioList({
          unitContentName: row?.contentName,
          scriptId: editId,
        })) as [any, AudioResponse]
        // 判断是否需要显示验听
        if (!same) {
          // 更新语料信息
          const latest: AudioItem | undefined = data?.scriptUnitContents?.find((item: AudioItem) => item.id === row.id)
          if (latest) {
            // 清空当前播放音频
            handleAudioPlay()
            // 显示验听弹窗
            uploadConfirmAudioItem.value = latest
            uploadConfirmDialogVisible.value = true
          }
        }
        // 更新列表
        await initData()
      } else {
        ElMessage.error(`${opt.file.name || ''}文件上传失败：${res ?? ''}`)
      }
    }
  } else {
    ElMessage.warning(`${row?.contentName || ''}文件上传失败：没有找到对应的语料名称`)
  }
  loading.value = false
}
// 批量上传音频
// 批量上传进度弹窗visible
const uploadProcessVisible = ref(false)
const uploadProcessInfo = ref<{ name: string, remark?: string }[]>([])
const successNumber = ref(0)
const fileList = ref([])
// 批量上传接口调用和处理
const uploadFileAction = async (opt: UploadRequestOptions) => {
  uploadProcessVisible.value = true
  if (opt.file.name.endsWith('.wav')) {
    const file = new FormData()
    file.append('file', opt.file)
    // 按文件名匹配语料名称，找打语料ID，上传音频
    const currentAudioItem: AudioItem | undefined = tableAllData.value?.find((item: AudioItem) => {
      // 判断名称时，移除.wav后缀
      return item.contentName === opt.file.name.slice(0, -4)
    })
    if (typeof currentAudioItem?.id === 'number') {
      // 文件名有对应的语料名称
      const params: AudioItemUploadParams = {
        contentId: currentAudioItem.id,
        file,
      }
      trace({ page: `话术编辑-音频管理-批量上传(${editId})`, params: opt.file?.name })
      const data = await scriptAudioModel.uploadMultipleAudioByOnce(params) as string
      if (!data.includes('成功')) {
        uploadProcessInfo.value.push({
          name: opt.file.name,
          remark: data
        })
      } else {
        successNumber.value = successNumber.value + 1
      }
    } else {
      // 文件名没有对应的语料名称
      uploadProcessInfo.value.push({
        name: opt.file.name,
        remark: '音频文件名与语料名称不匹配'
      })
    }
  } else {
    uploadProcessInfo.value.push({
      name: opt.file.name,
      remark: '校验不通过，音频类型必须为wav'
    })
  }
}
// 批量上传弹窗关闭
const closeUpload = (v: boolean) => {
  if (!v) {
    uploadProcessInfo.value = []
    fileList.value = []
    successNumber.value = 0
    initData()
  }
}

// 删除全部音频
const delAllAudioAction = async () => {
  loading.value = true
  trace({ page: `话术编辑-音频管理-删除全部音频(${editId})`, params: {id: editId} })
  const [err, _] = await to(scriptAudioModel.deleteAudioList({ id: editId }))
  !err && ElMessage.success('删除成功')
  initData()
  loading.value = false
}
const delAll = () => {
  Confirm({
    text: `您确定要删除全部音频吗?`,
    type: 'danger',
    title: '删除确认',
    confirmText: '删除'
  }).then(() => {
    delAllAudioAction()
  }).catch(() => {
  })
}

const tempAudio = reactive<AudioInfoItem>(new TempAudioOrigin())
const audioPlayStatus = ref<'pause' | 'play' | 'none'>('none')
const playRate = ref(1)
/**
 *  切换音频播放
 *  @param {number} flag 下一条： 1 ，上一条  -1
 *  @param {number} aId 当前播放的音频id
 */
const handleAudioChange = async (flag: -1 | 1, aId: number) => {
  const index = tableData.value?.findIndex(item => item.id == aId) || -1
  let index2: number = index === -1 ? 0 : (index + flag);
  if (index === -1 || index2 < 0 || index >= pageSize.value) {
    audioPlayStatus.value = 'pause'
    return Object.assign(tempAudio, new TempAudioOrigin())
  }
  const row = tableData.value ? tableData.value[index2] : null
  if (row) {
    if (row.audioPath) {
      Object.assign(tempAudio, {
        id: row.id,
        name: row.contentName,
        content: row.content,
        audioPath: row.audioPath,
        audioStatus: row.audioStatus,
        audioTag: row.audioTag,
      })
      if (!tempAudio.audioStatus || tempAudio.audioStatus === AudioStatusEnum['未验听']) {
        const [err, res] = await to(scriptCorpusModel.updateCorpusAudioStatus({ id: tempAudio.id! }))
        if (!err) {
          tableData.value![index2]!.audioStatus = res || AudioStatusEnum['未验听']
          tempAudio.audioStatus = res || AudioStatusEnum['未验听']
        }
      }
      return
    } else {
      handleAudioChange(flag, row.id || -1)
    }
  } else {
    audioPlayStatus.value = 'pause'
    return Object.assign(tempAudio, new TempAudioOrigin())
  }
}

const handleAudioTagChange = (tag: string) => {
  tempAudio.audioTag = tag
  if (!tableData.value) return
  const index = tableData.value?.findIndex(item => item.id === tempAudio.id) ?? -1
  if (index > -1) {
    tableData.value[index]!.audioTag = tag || ''
    tableData.value[index]!.audioStatus = AudioStatusEnum['已标记']
  }
}

const clearAudioMark = async (id: number) => {
  loading.value = true
  const params = { id: id, audioTag: '' }
  trace({ page: `话术编辑-音频管理-删除音频标记(${editId})`, params })
  const [err] = await to(scriptCorpusModel.saveAudioMark(params))
  !err && ElMessage.success('删除成功')
  initData()
}

const handleKeydown = (e: { code: string, preventDefault: Function }) => {
  if (['ArrowDown', 'ArrowUp',].includes(e.code)) {
    e.preventDefault();
  }
}
const handleKeyup = (e: { code: string }) => {
  const ida = tempAudio.id as number
  if (ida > 0) {
    if (['ArrowDown',].includes(e.code)) {
      handleAudioChange(1, ida)
    }
    if (['ArrowUp',].includes(e.code)) {
      handleAudioChange(-1, ida)
    }
  }
}

const handleAudioPlay = async (row?: AudioItem) => {
  if (!row) {
    // 关闭音频，不修改tempAudio的id，原因是：需要保持播放记录高亮。
    // 接口字段里语料名称使用contentName，音频播放器组件里使用name字段，这里保留兼容
    Object.assign(tempAudio, {
      name: '',
      content: '',
      audioPath: '',
      audioStatus: AudioStatusEnum['未验听'],
      audioTag: '',
    })
    audioPlayStatus.value = 'none'
    return
  }
  const isSame = tempAudio.id === row?.id && tempAudio.audioPath === row?.audioPath
  // 接口字段里语料名称使用contentName，音频播放器组件里使用name字段，这里保留兼容
  Object.assign(tempAudio, row ? {
    id: row.id,
    name: row.contentName,
    content: row.content,
    audioPath: row.audioPath,
    audioStatus: row.audioStatus,
    audioTag: row.audioTag,
  } : new TempAudioOrigin())
  if (isSame) {
    audioPlayStatus.value = audioPlayStatus.value == 'play' ? 'pause' : 'play'
  } else {
    if (!row.audioStatus || row.audioStatus === AudioStatusEnum['未验听']) {
      const [err, res] = await to(scriptCorpusModel.updateCorpusAudioStatus({ id: tempAudio.id! }))
      if (!err) {
        tempAudio.audioStatus = res || AudioStatusEnum['未验听']
        updatePlayStatus(tempAudio.id as number, res || AudioStatusEnum['未验听'])
      }
    }
  }
}
// 更新文字内容
const editCorpus: AudioItem = reactive({ id: undefined, contentName: '', content: '', audioPath: '' })
const corpusEditVisible = ref(false) // 编辑文字内容弹窗
const updateCorpusContent = async (row: AudioItem) => {
  corpusEditVisible.value = true
  if (!row || !row.id) {
    Object.assign(editCorpus, new TempAudioOrigin());
    return;
  }
  Object.assign(editCorpus, pickAttrFromObj(row, Object.keys(new TempAudioOrigin()) as unknown as (keyof AudioItem)[]))
}
// 更新验听状态（非同名语料验听跟进，文字内容编辑后）
const updatePlayStatus = (id: number, status: AudioStatusEnum) => {
  if (id && tableData.value) {
    const index = tableData.value?.findIndex(item => item.id === id)
    if (index !== -1) {
      tableData.value[index]!.audioStatus = status
    }
  }
}

// 批量更新文字内容
const fileRef = ref()
const batchEdit = () => {
  // @ts-ignore
  document.querySelector('.batch-edit')!.click()
}
const handleEditFileChange = async (e: Event) => {
  loading.value = true
  const params = await checkXlsContent(e) as {
    scriptId: number, // 话术ID
    corpusId: number, // 语料ID
    contentId: number, // 语句ID
    content: string, // 文字内容
  }[] | null
  if (!params?.length) {
    loading.value = false
    // @ts-ignore
    fileRef.value.value = null
    return
  }
  await trace({
    page: `话术制作-音频管理-批量编辑文字内容(${editId})：开始`,
    params
  })
  const errArr: any[] = []
  for (const item of params) {
    const [err] = await to(scriptCorpusModel.saveCorpusContent(item))
    err && errArr.push(err)
  }
  !errArr.length && ElMessage.success('操作成功')
  initData()
  loading.value = false
  // @ts-ignore
  fileRef.value.value = null
  await trace({
    page: `话术制作-音频管理-批量编辑文字内容(${editId})：完成`,
    params: '完成',
  })
}

const export2Excel = async () => {
  const list: AudioItem[] = tableRef.value?.getSelectionRows() || []
  if (!list || list.length === 0) {
    return ElMessage.warning('请选择需要下载的语料！')
  }
  const contentMap = new Map<string, number>([])
  const data = list?.map(item => {
    const { contentName, content, audioPath, uploadTime, corpusType, canvasName } = item
    let hasSameContent: boolean
    if (!contentMap.has(content ?? '')) {
      contentMap.set(content ?? '', 1)
      hasSameContent = false
    } else {
      hasSameContent = true
    }
    return {
      '语料名称': contentName,
      '文字内容': {
        v: content,
        s: hasSameContent ? { fill: { fgColor: { rgb: "E59000" } } } : undefined,
      },
      '语料类型': corpusType ? corpusTypeOption[corpusType as CorpusTypeEnum]?.name || '' : '',
      '所属流程': canvasName || '',
      '音频地址': audioPath || '',
      '更新时间': uploadTime || '',
    }
  })
  trace({ page: `话术编辑-音频管理-导出表格(${editId})`, params: data })
  exportExcelMarked(data, `【${scriptStore.name}】音频${dayjs().format('YYYY-MM-DD')}.xlsx`)
}

// 更新打断设置（整个语料）
const corpusContentVisible = ref(false)
const corpusId = ref<number | null>(null)
const updateCorpusInterrupt = async (row: AudioItem) => {
  if (!row || !row.corpusId) {
    return ElMessage.warning('所属语料ID不存在')
  }
  corpusId.value = row.corpusId
  corpusContentVisible.value = true
}

// 正在下载音频文件
const downloadingAudio = ref(false)

/**
 * 批量导出当前话术的全部音频文件
 */
const exportAllAudio = async () => {
  const list: AudioItem[] = tableRef.value?.getSelectionRows() || []
  if (!list?.length) {
    return ElMessage.warning('请选择需要下载的语料！')
  }
  const idList = list.flatMap((item: AudioItem) => {
    return item && item?.id ? [item.id] : []
  })
  try {
    downloadingAudio.value = true
    trace({ page: `话术编辑-音频管理-下载音频-批量(${editId})`, params: idList })
    const res = await scriptAudioModel.patchDownloadAudio(idList) as BlobPart
    // 单个音频下载wav，多个音频下载zip
    const fileName = idList.length > 1 ? scriptStore.name + '.zip' : (list?.at(0)?.contentName ?? '音频') + '.wav'
    exportFileByBlob(res, fileName, idList.length > 1 ? 'zip' : 'wav')
  } catch (e) {
    ElMessage.warning('下载过程发生错误')
  } finally {
    downloadingAudio.value = false
  }
}

// 音文相似度弹窗 是否显示
const similarDialogVisible = ref(false)
// 音文相似度弹窗 内容
const similarDialogData = ref<AudioItem>({})
/**
 * 点击音文相似度
 * @param item 点击的音频
 */
const onClickSimilarPercent = (item: AudioItem) => {
  // 更新数据
  similarDialogData.value = JSON.parse(JSON.stringify(item ?? {}))
  // 显示弹窗
  similarDialogVisible.value = true
}

// 上传确认弹窗
const uploadConfirmDialogVisible = ref(false)
// 需要确认的音频
const uploadConfirmAudioItem = ref<AudioItem | null>(null)

/**
 * 特殊逻辑双击删除，展示确认框
 * @param item 删除的音频（语句）所属得语料
 */
const handleDeleteCorpus = async (item: AudioItem) => {
  if (!item || !item.corpusId || !!isChecked) return
  const [err1] = await to(Confirm({
    text: `
      <p>您确定要删除该音频所属的语料嘛？</p>
      <p class="tw-text-[#E54B17] tw-font-[600]">请务必确认在画布或其他语料中未使用，否则提交审核将出现异常。</p>
    `,
    type: 'danger',
    title: '删除确认',
    confirmText: '删除'
  }))
  if (err1) return
  const params = {corpusId: item.corpusId, scriptId: editId}
  trace({ page: `话术编辑-音频管理-双击删除语料(${editId})`, params })
  const [err2] = await to(scriptCorpusModel.deleteOneScriptCorpusById(params))
  if (err2) return
  ElMessage.success('删除成功')
  initData()
}

const getRowClass = ({ row }: { row: AudioItem }) => {
  if (row.id === tempAudio.id) {
    return 'background-color: #BEDAFF'
  }
  return ''
}

// 执行
onActivated(() => {
  initData(true)
  document.addEventListener('keyup', handleKeyup)
  document.addEventListener('keydown', handleKeydown);
})
const clearData = () => {
  tableRef.value?.clearSelection()
  document.removeEventListener('keyup', handleKeyup);
  document.removeEventListener('keydown', handleKeydown);
  tableAllData.value = null
  tableData.value = null
  tableRef.value = null
  fileRef.value = null
}
onDeactivated(() => {
  audioPlayStatus.value = 'pause'
  clearData()
})
onBeforeRouteLeave(() => {
  audioPlayStatus.value = 'pause'
  clearData()
})

</script>

<style scoped lang="postcss">
.audio-container {
  width: 100%;
  height: calc(100vh - 170px);
  position: relative;
  box-sizing: border-box;
  padding: 16px 12px 8px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  font-size: 13px;
  .el-table {
    font-size: var(--el-font-size-base);
    :deep(.cell) {
      padding: 0 8px;
    }
  }
  :deep(.el-upload-list) {
    display: none;
  }
  :deep(.el-table .caret-wrapper) {
    display: none;
  }
  :deep(.el-table--enable-row-hover .el-table__body tr:hover > td) {
    background-color: rgba(0, 0, 0, 0) !important;
  }
}
.item {
  font-size: 14px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-right: 10px;
  span {
    width: 90px
  }
}
.audio-text-similar {
  background: transparent;
  &:hover {
    font-weight: bold;
  }
}

</style>
