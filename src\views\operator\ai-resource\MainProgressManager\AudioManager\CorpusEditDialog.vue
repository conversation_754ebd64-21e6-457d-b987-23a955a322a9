<template>
  <el-dialog
    v-model="dialogVisible"
    :close-on-click-modal="false"
    align-center
    width="600px"
    @close="cancel"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{ addData.contentName }}</div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-px-[12px]"
    >
      <el-form
        :model="addData"
        :rules="rules"
        label-width="80px"
        ref="editRef"
        :disabled="isChecked"
      >
        <el-form-item label="语料名称：" prop="name">
          <el-input v-model="addData.contentName" clearable placeholder="请输入语料名称（40字以内）" maxlength="40" disabled/>
        </el-form-item>
        <el-form-item label="语料类型：" prop="corpusType">
          <el-select
            v-model="addData.corpusType"
            placeholder="选择语料类型"
            style="width:472px;"
            disabled
          >
            <el-option
              v-for="(item, key) in corpusTypeOption"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="文字内容：" prop="content">
          <el-input v-model.trim="addData.content" type="textarea" :autosize="{minRows: 2, maxRows: 6}" placeholder="请输入语料的文本内容（300字以内）" maxlength="300" clearable/>
        </el-form-item>
        <el-form-item label="音频：" prop="audioPath">
          <el-button v-if="addData?.id && tempAudio.id == addData.id && audioPlayStatus == 'play'" link type="primary" :disabled="!addData.audioPath" @click="handleAudioPlay(addData)">
            <el-icon :size="20"><svg-icon name="stop-circle"></svg-icon></el-icon>
          </el-button>
          <el-button v-else :type="addData.audioPath ? 'primary':'default'" :disabled="!addData.audioPath" link @click="handleAudioPlay(addData)">
            <el-icon :size="20"><svg-icon name="play-circle"></svg-icon></el-icon>
          </el-button>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <MiniAudio
      v-if="tempAudio.id && tempAudio.id > 0"
      v-model:audioStatus="audioPlayStatus"
      v-model:playRate="playRate"
      :tempAudio="tempAudio"
      @close="handleAudioPlay()"
    />
    <template #footer>
      <span v-if="!isChecked">
        <el-button @click="cancel" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="confirm" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, reactive, defineAsyncComponent, } from 'vue'
import { AudioItem, corpusTypeOption, AudioStatusEnum } from '@/type/speech-craft'
import { ElMessage, } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { scriptCorpusModel, } from '@/api/speech-craft'
import { useScriptStore } from '@/store/script'
import { CloseBold, Select } from '@element-plus/icons-vue'
import to from 'await-to-js';
import { TempAudioOrigin, AudioInfoItem } from './constant';
import { trace } from '@/utils/trace'

const MiniAudio = defineAsyncComponent({
  loader:() => {
    return import('@/components/MiniAudio.vue')
  }
})
const scriptStore = useScriptStore()
const isChecked = scriptStore.isChecked
const scriptId = scriptStore.id
const loading = ref(false)
const emits = defineEmits(['update:visible', 'confirm', 'play'])
const props = defineProps<{
  corpusData: AudioItem;
  visible: boolean
}>();
const dialogVisible = ref<boolean>(false)
const addData = reactive<AudioItem>(props.corpusData)
watch(() => props.visible, n => {
  dialogVisible.value = n
  if (n) {
    Object.assign(addData, props.corpusData)
    audioPlayStatus.value = 'pause'
  }

})
const audioPlayStatus = ref<'pause' | 'play' | 'none'>('none')
const playRate = ref(1)
const editRef = ref<FormInstance  | null>(null)
// 内容名称 校验函数
const validateContent = (rule: any, value: any, callback: any) => {
  const reg = /[$]/
  if (reg.test(value)) {
    callback(new Error('文字内容请勿出现特殊符号$'))
  }
  callback()
}
const rules = {
  content: [
    { required: true, message: '请输入文字内容', trigger: 'blur' },
    { validator: validateContent, trigger: 'blur' },
  ],
}
// 底部确认、取消
const confirm = async () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      const params = {
        contentId: addData.id as number,
        content: addData.content as string,
        scriptId: scriptId,
        corpusId: addData.corpusId as number,
      }
      trace({
        page: `话术编辑-音频管理-编辑文字内容(${scriptId})`,
        params
      })
      const [err, _] = await to(scriptCorpusModel.saveCorpusContent(params))
      if (!err) {
        ElMessage.success('操作成功')
        emits('confirm')
        cancel()
      }
      loading.value = false
    }
  })
}

const tempAudio = reactive<AudioInfoItem>(new TempAudioOrigin())
const handleAudioPlay = async (row?: AudioItem) => {
  const isSame = tempAudio.id === row?.id && tempAudio.audioPath === row?.audioPath
  // 接口字段里语料名称使用contentName，音频播放器组件里使用name字段，这里保留兼容
  Object.assign(tempAudio, row ? {
    id: row.id,
    name: row.contentName,
    content: row.content,
    audioPath: row.audioPath,
    audioStatus: row.audioStatus,
  } : new TempAudioOrigin())
  if (isSame) {
    audioPlayStatus.value = audioPlayStatus.value == 'play' ? 'pause' : 'play'
  } else {
    audioPlayStatus.value = 'play'
    if (!addData.audioStatus || addData.audioStatus === AudioStatusEnum['未验听']) {
      const [err, res] = await to(scriptCorpusModel.updateCorpusAudioStatus({id: tempAudio.id!}))
      if (!err) {
        addData.audioStatus = res || AudioStatusEnum['未验听']
        emits('play', addData.id, addData.audioStatus)
      }
    }
  }
}
const cancel = () => {
  dialogVisible.value = false
  Object.assign(tempAudio, new TempAudioOrigin())
  emits('update:visible', false)
}
</script>

<style lang="postcss" type="text/postcss" scoped>
.el-input-number .el-input__inner {
  text-align: left;
}
.text {
  font-size: 14px;
  line-height: 24px;
  margin-right: 10px;
  text-align: left;
}
.el-form {
  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 16px;
    &:first-child {
      margin-top: 14px;
    }
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
  :deep(.el-form-item__content) {
    font-size: var(--el-font-size-base);
  }
}
</style>
