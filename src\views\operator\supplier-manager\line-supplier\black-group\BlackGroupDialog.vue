<template>
  <el-dialog
    v-model="dialogVisible"
    width="70%"
    class="supplier-dialog"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <template #header>
      <div class="form-dialog-header">
        {{ props.data?.id === undefined || props.data?.id === null ? '新增' : '编辑' }}黑名单挂载
      </div>
    </template>

    <el-scrollbar class="form-dialog-main">
      <div class="form-dialog-main-inner">
        <el-form
          ref="formRef"
          label-width="90px"
          label-position="right"
          :model="form"
          :rules="rules"
        >
          <el-form-item label="生效范围：" prop="supplyLineNumbers">
            <el-radio-group v-model="form.allLinesActive" @change="onChangeScopeIsAll">
              <el-radio :label="true">
                全部
              </el-radio>
              <el-radio :label="false">
                部分
              </el-radio>
              <SelectBox
                v-show="!form.allLinesActive"
                v-model:selectVal="form.supplyLineNumbers"
                :options="lineAllList||[]"
                name="lineName"
                val="lineNumber"
                placeholder="供应线路"
                style="width: 300px; max-width: 300px; margin: 0;"
                filterable
                class="tw-flex-grow"
                multiple
                canSelectAll
              />
            </el-radio-group>
          </el-form-item>
          <el-form-item label="挂载黑名单：" prop="blackListGroupId">
            <el-select
              v-model="form.blackListGroupId"
              placeholder="选择黑名单分组"
              style="width: 300px;"
              filterable
              @change="onChangeSelectedBlackList"
            >
              <el-option
                v-for="item in availableList"
                :key="item.id"
                :label="item.groupName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <el-table
            class="tw-w-full tw-mt-[12px]"
            :header-cell-style="tableHeaderStyle"
            stripe
            :data="selectedList"
          >
            <el-table-column align="left" fixed="left" prop="groupName" label="分组名称" width="180" show-overflow-tooltip />
            <el-table-column align="left" prop="limitDuration" label="限制时长" min-width="100" show-overflow-tooltip>
              <template #default="{row}:{row:SupplierBlackGroupInfo}">
                <template v-if="row?.limitDuration===-1">
                  永久生效
                </template>
                <span v-else>
                  {{ row?.limitDuration }} 天
                </span>
              </template>
            </el-table-column>
            <el-table-column label="人群类别" align="left" width="120" prop="targetType" :formatter="formatterEmptyData" show-overflow-tooltip>
              <template #default="{ row }">
                {{ findValueInEnum(row?.targetType, BlackPersonTypeEnum) || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="人群等级" align="left" min-width="80" prop="targetLevel" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
            <el-table-column label="分组规则" align="left" min-width="200" prop="targetComment" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
            <el-table-column label="影响接通" align="left" min-width="200" prop="putThroughComment" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
            <el-table-column label="影响收益" align="left" min-width="200" prop="costBenefitComment" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
            <el-table-column label="影响本收" align="left" min-width="200" prop="costBenefitComment" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
            <el-table-column label="备注" align="left" min-width="200" prop="comment" :formatter="formatterEmptyData" show-overflow-tooltip></el-table-column>
            <!-- <el-table-column align="center" prop="createTime" label="创建时间" min-width="170">
              <template #default="{row}:{row:SupplierBlackGroupInfo}">
                {{ row?.createTime ? dayjs(row?.createTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
              </template>
            </el-table-column>
            <el-table-column align="center" prop="updateTime" label="更新时间" min-width="170">
              <template #default="{row}:{row:SupplierBlackGroupInfo}">
                {{ row?.updateTime ? dayjs(row?.updateTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
              </template>
            </el-table-column> -->
          </el-table>
        </el-form>
      </div>
    </el-scrollbar>

    <template #footer>
      <div class="form-dialog-footer">
        <el-button :icon="CloseBold" :disabled="loadingConfirm" @click="closeDialog">
          取消
        </el-button>
        <el-button type="primary" :icon="Select" :loading="loadingConfirm" @click="handleConfirm">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, reactive, ref, watch } from 'vue'
import { findValueInEnum, Throttle, formatterEmptyData } from '@/utils/utils'
import { ElMessage, } from 'element-plus'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { tableHeaderStyle } from '@/assets/js/constant'
import { SearchLineParams, SupplierBlackGroupInfo, SupplierBlackGroupParam, SupplierLineInfo } from '@/type/supplier'
import { supplierModel } from '@/api/supplier'
import { useSupplierStore } from '@/store/supplier'
import to from 'await-to-js'
import { rules, SupplierBlackGroupOrigin } from './constant'
import { BlackListGroupItem, BlackPersonTypeEnum } from '@/type/dataFilter'


// ---------------------------------------- 通用 开始 ----------------------------------------

const props = defineProps<{
  visible: boolean,
  data: SupplierBlackGroupInfo | null,
  availableBlacklist: BlackListGroupItem[] | null,
}>()
const emits = defineEmits([
  'update:visible',
  'confirm'
])

// 弹窗显示隐藏
// props.visible由父组件维护，子组件不能直接修改，dialogVisible由子组件维护
const dialogVisible = ref<boolean>(props.visible)

watch(
  () => props.visible,
  async (val) => {
    dialogVisible.value = val
    // 每次显示弹窗时
    if (val) {
      await nextTick()
      resetForm()
      // 更新可选黑名单
      availableList.value = props.availableBlacklist || []

      if (!props.data) {
        return
      }

      // 更新表单数据
      form.allLinesActive = !!props.data.isAllLinesActive
      form.blackListGroupId = props.data.id
      form.supplyLineNumbers = props.data.supplyLines?.map(line => line?.lineNumber ?? '') || []

      // 更新展示列表
      const currentBlacklist = availableList.value.find(item => item.id === props.data?.id)
      selectedList.value = currentBlacklist ? [currentBlacklist] : []
    }
  }
)

const supplierStore = useSupplierStore()

// ---------------------------------------- 通用 结束 ----------------------------------------

// ---------------------------------------- 表单 开始 ----------------------------------------

// 正在提交
const loadingConfirm = ref<boolean>(false)
// 提交节流锁
const throttleConfirm = new Throttle(loadingConfirm)
// 表单DOM
const formRef = ref()

// 表单数据
const form: SupplierBlackGroupParam = reactive(new SupplierBlackGroupOrigin(supplierStore.currentSupplier.id ?? -1))
// 表单校验规则

/**
 * 提交表单
 */
const submit = async () => {
  // 节流锁上锁
  if (throttleConfirm.check()) {
    return
  }
  throttleConfirm.lock()

  // 处理参数
  const params: SupplierBlackGroupParam = {
    callLineSupplierId: form.callLineSupplierId ?? supplierStore.currentSupplier.id ?? undefined,
    blackListGroupId: form.blackListGroupId ?? props.data?.id ?? undefined,
    allLinesActive: !!form.allLinesActive,
    supplyLineNumbers: form.supplyLineNumbers ?? [],
  }
  // 请求接口
  const [err,] = <[any, SupplierBlackGroupInfo]>await to(supplierModel.saveBlacklist(params))
  // 节流锁解锁
  throttleConfirm.unlock()

  if (err) return
  ElMessage.success('保存成功')
  // 通知父组件更新
  emits('confirm')
  // 关闭弹窗
  closeDialog()
}
/**
 * 重置表单
 */
const resetForm = () => {
  // 表单数据恢复默认值
  Object.assign(form, new SupplierBlackGroupOrigin(supplierStore.currentSupplier.id ?? -1))
  // 默认值为空，会触发表单校验并提示，所以需要立即清除表单的校验结果
  formRef.value?.resetFields()
  // 清空其它内容
  selectedList.value = []
  availableList.value = []
}

/**
 * 关闭弹窗，主动触发
 */
const closeDialog = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
/**
 * 点击确定按钮
 */
const handleConfirm = () => {
  formRef.value?.validate(async (valid: boolean) => {
    // 校验通过
    if (valid) {
      submit()
    }
  })
}

// ---------------------------------------- 表单 结束 ----------------------------------------

// ---------------------------------------- 生效范围 开始 ----------------------------------------

// 线路列表，正在加载
const loading = ref(false)

// 线路列表，接口数据
const lineAllList = ref<SupplierLineInfo[]>([])

/**
 * 更新线路列表，全部，接口数据
 */
const updateLineAllList = async () => {
  loading.value = true
  // 处理参数
  const params = { callLineSupplierId: supplierStore.currentSupplier.id ?? -1 }

  const [err, res] = await to(supplierModel.getLineList(params)) as [Error | null, SupplierLineInfo[] | null]
  lineAllList.value = res || []

  loading.value = false
}

/** 更改生效范围 */
const onChangeScopeIsAll = (val: boolean) => {
  form.allLinesActive = val ?? false
  if (val) {
    form.supplyLineNumbers = lineAllList.value.map(item => item?.lineNumber ?? '')
  }
}

// ---------------------------------------- 生效范围 结束 ----------------------------------------

// ---------------------------------------- 黑名单分组 开始 ----------------------------------------

// 可选黑名单分组列表
const availableList = ref<SupplierBlackGroupInfo[]>([])

// 已选黑名单分组列表
const selectedList = ref<SupplierBlackGroupInfo[]>([])


/**
 * 可添加黑名单分组列表 下拉选择框切换显示隐藏
 * @param {boolean} visible 显示隐藏状态
 */
const onChangeSelectedBlackList = async () => {
  const currentItem = availableList.value.find((item: SupplierBlackGroupInfo) => {
    return item.id === form.blackListGroupId
  })
  selectedList.value = currentItem ? [currentItem] : []
}

// ---------------------------------------- 黑名单分组 结束 ----------------------------------------

// 更新其他内容
onMounted(async () => {
  await updateLineAllList()
})

</script>

<style lang="postcss" scoped>
</style>
