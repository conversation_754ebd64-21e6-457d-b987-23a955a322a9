{"name": "ai-report", "version": "8.4.0", "scriptsComment": {"dev": "本地开发环境（接口地址**************）", "dev.110": "本地开发环境（接口地址***************）", "dev.111": "本地开发环境（接口地址***************）", "dev.113": "本地开发环境（接口地址***************）", "test.uat": "保泰测试环境（UAT）", "test.85": "公司开发环境（线上版本）", "test.110": "公司开发环境（***************）", "test.111": "公司开发环境（***************）", "test.113": "公司开发环境（***************）", "test.125": "公司开发环境（**************）", "build": "生产环境（实际业务）", "build2": "生产环境（压力测试）", "build.internal": "生产环境（虚拟桌面内部使用）", "build.manual-call.2": "生产环境（人工外呼，第2套环境）", "build-all": "生产环境（同时打包外网和虚机）"}, "scripts": {"dev": "vite --host", "dev.110": "vite --host --port 2002 --mode development.110", "dev.111": "vite --host --port 2002 --mode development.111", "dev.113": "vite --host --port 2002 --mode development.113", "test.uat": "vite build --mode test", "test.85": "vite build --mode test.85 && node ./auto-upload.js test.85", "test.110": "vite build --mode test.110 && node ./auto-upload.js test.110", "test.111": "vite build --mode test.111 && node ./auto-upload.js test.111", "test.113": "vite build --mode test.113 && node ./auto-upload.js test.113", "test.125": "vite build --mode test.125 && node ./auto-upload.js test.125", "build": "vite build --mode production", "build2": "vite build --mode production2", "build.internal": "vite build --mode production.internal", "build.manual-call.2": "vite build --mode production.manual-call.2", "build-all": "node ./build.js"}, "type": "module", "dependencies": {"@antv/x6": "^2.0.0", "@antv/x6-plugin-clipboard": "^2.0.0", "@antv/x6-plugin-history": "^2.0.0", "@antv/x6-plugin-keyboard": "^2.0.0", "@antv/x6-plugin-scroller": "^2.0.0", "@antv/x6-plugin-selection": "^2.0.0", "@antv/x6-plugin-snapline": "^2.0.0", "@antv/x6-plugin-transform": "^2.0.0", "@element-plus/icons-vue": "^2.0.10", "@tailwindcss/line-clamp": "^0.4.2", "@tiptap/extension-character-count": "^2.0.3", "@tiptap/extension-document": "^2.0.3", "@tiptap/extension-highlight": "^2.0.3", "@tiptap/extension-mention": "^2.9.1", "@tiptap/extension-placeholder": "^2.0.3", "@tiptap/extension-text-align": "^2.0.3", "@tiptap/pm": "^2.0.3", "@tiptap/starter-kit": "^2.0.3", "@tiptap/vue-3": "^2.0.3", "ant-design-vue": "4.x", "await-to-js": "^3.0.0", "axios": "^0.27.2", "clipboard": "^2.0.11", "dayjs": "^1.11.7", "echarts": "^5.4.1", "element-plus": "2.4.3", "file-saver": "^2.0.5", "janus-gateway": "^1.2.1", "jquery": "^3.6.1", "jszip": "^3.10.1", "pinia": "^2.0.16", "pinia-plugin-persistedstate": "^3.0.2", "postcss-loader": "^7.0.1", "quill-delta": "^5.1.0", "rollup-plugin-commonjs": "^10.1.0", "sortablejs": "^1.15.0", "vue": "^3.5.18", "vue-router": "^4.1.2", "webrtc-adapter": "^8.2.3", "xe-utils": "^3.5.7", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.1.0", "@types/file-saver": "^2.0.5", "@types/node": "^16.11.45", "@types/sortablejs": "^1.15.0", "@vicons/ionicons5": "^0.12.0", "@vitejs/plugin-vue": "^5.0.0", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.0", "@vue/tsconfig": "^0.8.1", "archiver": "^7.0.1", "autoprefixer": "^10.4.13", "chalk": "^5.2.0", "cross-env": "^7.0.3", "eslint": "^8.5.0", "eslint-plugin-vue": "^9.0.0", "express": "^4.18.2", "inquirer": "^10.0.1", "npm-run-all": "^4.1.5", "ora": "^8.0.1", "postcss": "^8.4.14", "postcss-import": "^14.1.0", "postcss-nesting": "^10.2.0", "ssh2-sftp-client": "^10.0.3", "tailwindcss": "^3.0.24", "typescript": "~4.7.4", "unplugin-auto-import": "^0.13.0", "unplugin-element-plus": "^0.4.1", "unplugin-icons": "^0.15.2", "unplugin-vue-components": "^0.23.0", "vite": "^5.4.19", "vite-plugin-cdn-import": "^0.3.5", "vite-plugin-mkcert": "^1.16.0", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^0.38.8"}, "packageManager": "yarn@4.9.3+sha512.8295ee814f6b253e16f516416481b481a215ed03ef1ae38524d108084872560a9ed75aeb23b91ab64222062ac4149a594150ae538c2a9536fdbcefd4e49b11cc"}