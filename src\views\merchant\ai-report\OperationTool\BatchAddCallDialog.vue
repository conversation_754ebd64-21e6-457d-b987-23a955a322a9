<template>
  <el-dialog
    v-model="dialogVisible"
    width="600px"
    align-center
    :show-close="!loading"
    :close-on-press-escape="false"
    @close="cancel"
    :close-on-click-modal="false"
  >
    <template #header>
      <div class="tw-pr-2 tw-flex tw-justify-between">
        <span class="tw-text-left tw-text-[16px] tw-font-semibold tw-text-[--primary-black-color-600]">{{props.isSingle ? '添加呼叫' : '批量添加呼叫'}}</span>
      </div>
    </template>
    <el-scrollbar
      v-if="stepNo===0"
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-p-[12px]"
    >
      <el-form
        :model="editData"
        :rules="rules"
        label-width="100px"
        ref="editRef"
      >
        <template v-if="!props.isSingle">
          <div class="tw-text-left tw-font-[600]">筛选方案</div>
          <el-form-item v-if="stepNo===0" label="筛选类型：">
            <el-select
              v-model="filterType"
              placeholder="筛选类型"
              clearable
              class="tw-w-full"
            >
              <el-option label="名单管理" :value="1"/>
              <el-option label="通话记录" :value="2"/>
            </el-select>
            <div v-show="filterType===2" class="tw-absolute tw-bottom-[-25px] tw-left-0 tw-text-left tw-text-[12px] tw-text-[var(--primary-orange-color)]">
              如任务已补呼，同一号码可能存在多条通话记录，请确保筛选项的合理性
            </div>
          </el-form-item>
          
        </template>
        <div class="tw-text-left tw-font-[600]">筛选项</div>
        <el-form-item v-if="filterType===2" label="标签：" prop="intentionLabelName">
          <el-input
            v-model="editData.intentionLabelName"
            placeholder="请输入标签"
          >
          </el-input>
        </el-form-item>

        <el-form-item label="号码/名单：" prop="phone">
          <el-input
            v-model="editData.phone"
            placeholder="请输入号码/名单编号"
          >
          </el-input>
        </el-form-item>
        <el-form-item v-if="filterType===2" label="呼出时间：" prop="calloutStartTime">
          <TimePickerBox
            v-model:start="editData.calloutStartTime"
            v-model:end="editData.calloutEndTime"
            placeholder="呼出时间"
            class="tw-w-full"
            format="YYYY-MM-DD HH:mm:ss"
            :maxRange="60*60*24*31*1000"
            :clearable="false"
          />
        </el-form-item>
        <template v-else>
          <el-form-item label="加入时间：" prop="addStartTime">
            <TimePickerBox
              v-model:start="editData.addStartTime"
              v-model:end="editData.addEndTime"
              placeholder="加入时间"
              format="YYYY-MM-DD HH:mm:ss"
              class="tw-w-full"
              :maxRange="60*60*24*31*1000"
              :clearable="false"
            />
          </el-form-item>
          <el-form-item label="最后外呼时间：" prop="lastCallStartTime">
            <TimePickerBox
              v-model:start="editData.lastCallStartTime"
              v-model:end="editData.lastCallEndTime"
              placeholder="最后外呼"
              format="YYYY-MM-DD HH:mm:ss"
              class="tw-w-full"
              :clearable="true"
            />
          </el-form-item>
        </template>
        <template v-if="filterType===1">
          <el-form-item prop="putThroughNumRight" label="接通次数：">
            <div class="tw-flex tw-items-center tw-justify-between tw-w-full">
              <InputNumberBox v-model:value="editData.putThroughNumLeft" placeholder="最小" style="width: 45%" append="次"/>
              <span>&nbsp;至&nbsp;</span>
              <InputNumberBox v-model:value="editData.putThroughNumRight" placeholder="最大" style="width: 45%" append="次" :min="editData.putThroughNumLeft"/>
            </div>
          </el-form-item>
          <el-form-item prop="calledNumLeft" label="拨打次数：">
            <div class="tw-flex tw-items-center tw-justify-between tw-w-full">
              <InputNumberBox v-model:value="editData.calledNumLeft" placeholder="最小" style="width: 45%" append="次"/>
              <span>&nbsp;至&nbsp;</span>
              <InputNumberBox v-model:value="editData.calledNumRight" placeholder="最大" style="width: 45%" append="次" :min="editData.calledNumLeft"/>
            </div>
          </el-form-item>
        </template>
        <template v-else>
          <el-form-item prop="callDurationLeft" label="通话时长：">
            <div class="tw-flex tw-items-center tw-justify-between tw-w-full">
              <InputNumberBox v-model:value="editData.callDurationLeft" placeholder="最小" style="width: 45%" append="秒"/>
              <span>&nbsp;至&nbsp;</span>
              <InputNumberBox v-model:value="editData.callDurationRight" placeholder="最大" style="width: 45%" append="秒" :min="editData.callDurationLeft"/>
            </div>
          </el-form-item>
          <el-form-item prop="cycleCountLeft" label="对话轮数：">
            <div class="tw-flex tw-items-center tw-justify-between tw-w-full">
              <InputNumberBox v-model:value="editData.cycleCountLeft" placeholder="最小" style="width: 45%" append="轮"/>
              <span>&nbsp;至&nbsp;</span>
              <InputNumberBox v-model:value="editData.cycleCountRight" placeholder="最大" style="width: 45%" append="轮" :min="editData.cycleCountLeft"/>
            </div>
          </el-form-item>
          <el-form-item prop="sayCountLeft" label="客户说话：">
            <div class="tw-flex tw-items-center tw-justify-between tw-w-full">
              <InputNumberBox v-model:value="editData.sayCountLeft" placeholder="最小" style="width: 45%" append="次"/>
              <span>&nbsp;至&nbsp;</span>
              <InputNumberBox v-model:value="editData.sayCountRight" placeholder="最大" style="width: 45%" append="次" :min="editData.sayCountLeft"/>
            </div>
          </el-form-item>
        </template>
        <el-form-item v-if="filterType===2" prop="callStatusArr" label="呼叫状态：">
          <el-select
            v-model="callStatusArr"
            placeholder="呼叫状态"
            clearable multiple collapse-tags
            collapse-tags-tooltip
            class="tw-w-full"
            :max-collapse-tags="3"
          >
            <el-option v-for="item in callStatusOptions" :key="item.name" :label="item.name" :value="item.value"/>
          </el-select>
        </el-form-item>
        <el-form-item label="范围选择：">
          <div class="tw-flex tw-justify-between tw-w-full">
            <el-select v-model="editData.operator" placeholder="运营商" clearable class="tw-w-[140px]">
              <el-option v-for="item in operatorList" :key="item" :label="item" :value="item"/>
            </el-select>
            <el-select v-model="province" placeholder="省" clearable class="tw-w-[140px]">
              <el-option v-for="item in provinceList" :key="item.split(',')[0]" :label="item.split(',')[0]" :value="item"/>
            </el-select>
            <el-select v-model="city" placeholder="市" clearable class="tw-w-[140px]">
              <el-option v-for="item in cityList" :key="item.split(',')[0]" :label="item.split(',')[0]" :value="item"/>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item v-if="filterType===2" prop="ifSendSms" label="触发短信：">
          <el-radio-group v-model="editData.ifSendSms">
            <el-radio label="是">是</el-radio>
            <el-radio label="否">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <div class="tw-text-left tw-font-[600]">号码处理</div>
        <el-form-item v-if="stepNo===0" label="排序方式：">
          <el-select
            v-model="editData.orderByTime"
            placeholder="请选择号码排序方式"
            class="tw-w-full"
          >
            <el-option label="不排序" value="none"/>
            <el-option label="倒序（晚呼早加入）" value="desc"/>
            <el-option label="正序（晚呼后加入）" value="asc"/>
          </el-select>
        </el-form-item>
        <el-form-item v-if="stepNo===0 && filterType===2" label="名单排除：">
          <el-select
            v-model="editData.excludePutThrough"
            placeholder="请选择名单排除方式"
            class="tw-w-full"
          >
            <el-option label="排除接通名单" value="1"/>
            <el-option label="不排除" value="0"/>
          </el-select>
        </el-form-item>
      </el-form>
    </el-scrollbar>
    <el-scrollbar v-if="stepNo!==0"
      :max-height="'calc(100vh - 200px)'"
      view-class="tw-flex tw-flex-col tw-p-[16px] tw-leading-[30px] tw-text-justify"
    >
      <div class="tw-font-[600]">筛选结果</div>
      <div>共有【{{ filterType === 2 ? '通话记录' : '名单管理' }}】的{{ total }}条数据满足筛选条件</div>
      <div v-if="confirmTips?.length" class="tw-text-[var(--primary-red-color)] tw-font-[600]">
        <div v-for="item in confirmTips" :key="item">{{ item }}</div>
      </div>
      <template v-if="stepNo===2">
        <div class="tw-font-[600]">任务执行情况</div>
        <div class="tw-w-[84%] tw-flex normal-progress">
          <span class="tw-flex-shrink-0 tw-mr-1">完成进度：</span>
          <el-progress
            :stroke-width="12"
            :percentage="(successNumber+failTable?.length)*tableData.length>0 ? Math.round(100*(successNumber+failTable?.length)/tableData.length) : 0"
            class="tw-w-full"
          >
            <span class="tw-text-[13px]">{{ successNumber+failTable?.length }}</span>
            <span class="tw-mx-[2px] tw-text-[13px]">/</span>
            <span class="tw-text-[13px]">{{ tableData.length || 0 }}</span>
          </el-progress>
        </div>
        
        <div class="tw-text-[var(--el-color-primary)]">
          <span>成功任务：</span>
          <span class="tw-text-[#165DFF]">{{ successNumber || 0 }}</span>
        </div>
        <div class="tw-text-[var(--primary-red-color)]">
          <span>失败任务：</span>
          <span class="tw-text-[#E54B17]">{{ failTable?.length || 0 }}</span>
        </div>
      </template>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <template v-if="stepNo===0">
          <el-button @click="cancel">取消</el-button>
          <el-button :loading="loading" type="primary" @click="filterAction">筛选</el-button>
        </template>
        <template v-if="stepNo===1">
          <el-button @click="stepNo=0">重新筛选</el-button>
          <el-button :loading="loading" type="primary" @click="confirm">提交</el-button>
        </template>
        <template v-if="stepNo===2">
          <el-button v-if="failTable?.length>0 && isFinish" link @click="exportFail">
            <el-icon><SvgIcon name="download3" color="none"></SvgIcon></el-icon>导出失败
          </el-button>
          <el-button :loading="!isFinish" @click="finishAction">{{isFinish ? '关闭' : '执行中'}}</el-button>
        </template>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, reactive, watch} from 'vue'
import { TaskCallSearchModal, TaskTypeEnum, TaskManageItem, CallStatusEnum2 } from '@/type/task'
import { OperatorEnum,  } from '@/type/common'
import dayjs from 'dayjs'
import { useGlobalStore } from '@/store/globalInfo'
import { aiOutboundTaskModel,  } from '@/api/ai-report'
import type { FormInstance, } from 'element-plus'
import { storeToRefs } from 'pinia'
import { pickAttrFromObj, enum2Options, findValueInEnum } from '@/utils/utils'
import { ResponseData } from '@/axios/request/types'
import InputNumberBox from '@/components/InputNumberBox.vue'
import TimePickerBox from '@/components/TimePickerBox.vue'
import { ElMessage } from 'element-plus'
import { trace } from '@/utils/trace';

const emits = defineEmits(['update:visible', 'confirm', 'confirmEnd', 'export'])
const globalStore = useGlobalStore()
const  {loading} = storeToRefs(globalStore)
const props = withDefaults(defineProps<{
  visible: boolean,
  isSingle?: boolean, // 是否单个任务名单添加呼叫
  tableData: TaskManageItem[],
  taskType: TaskTypeEnum,
}>(), {
  isSingle: false,
})
const visible = ref(props.visible)
const tableData = ref(props.tableData || [])
const filterType = ref(1) // 1:名单管理 2:通话记录
const successNumber = ref(0)
const failTable = ref<(TaskManageItem & {remark: string})[]>([])
const isFinish = computed(() => tableData.value.length === failTable.value.length + successNumber.value)
class TaskCallOrigin {
  phone = ''
  operator = undefined
  province = undefined
  city = undefined
  ifSendSms = undefined
  intentionLabelName = undefined
  callStatus = undefined
  cycleCountLeft = undefined
  cycleCountRight = undefined
  sayCountLeft = undefined
  sayCountRight = undefined
  calledNumLeft = undefined
  calledNumRight = undefined
  putThroughNumLeft = 0
  putThroughNumRight = 0
  calloutStartTime = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss')
  calloutEndTime = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
  addStartTime = dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss')
  addEndTime = dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
  lastCallStartTime = undefined
  lastCallEndTime = undefined
  orderByTime = 'none'
  excludePutThrough = '1'
  ifFastRecall = '1'
  intentionLabelNameContains = undefined
}
const editData = reactive<TaskCallSearchModal>(new TaskCallOrigin())
const provinceList = ref<string[]>([])
const operatorList = Object.values(OperatorEnum)
const dialogVisible = ref(visible)
const editRef = ref<FormInstance  | null>(null)
const stepNo = ref(0)
const total = ref(0)
const cancel = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}
const rules = {
  putThroughNumRight: [{ required: true, message: '请输入接通次数', trigger: 'blur' }],
  callStatusArr: [{
    validator: (rule: any, value: any, callback: any) => {
      if (!callStatusArr.value || callStatusArr.value.length === 0) {
        return callback(new Error('请选择呼叫状态'))
      }
      return callback()
    }, trigger: 'change'
  }]
}

// 筛选操作
const callStatusArr = ref<string[]>([])
const filterAction = () => {
  editRef.value && editRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      editData.callStatus = (callStatusArr.value && callStatusArr.value.length > 0) ? callStatusArr.value.join(',') : ''
      editData.intentionLabelNameContains = editData.intentionLabelName ? 'contains' : undefined
      try {
        const params = {
          taskId: tableData.value?.map(item => item.id).join(','),
          ...pickAttrFromObj(editData, filterType.value === 2 ? [
            'phone', 'intentionLabelName', 'intentionLabelNameContains', 'whoHangup', 'calloutStartTime', 'calloutEndTime', 'calledNumLeft', 'calledNumRight', 'ifSendSms', 'sayCountLeft', 'sayCountRight',
            'cycleCountLeft', 'cycleCountRight', 'callDurationLeft', 'callDurationRight', 'operator', 'province', 'city', 'callStatus', 'ifFastRecall', 'excludePutThrough',
          ] : [
            'phone', 'operator', 'province', 'city', 'calledNumLeft', 'calledNumRight', 'putThroughNumLeft', 'putThroughNumRight',
            'addStartTime', 'addEndTime', 'lastCallStartTime', 'lastCallEndTime',
          ]),
        }
        let res
        await trace({
          page: !props.isSingle
            ? `外呼工具-批量添加呼叫-${tableData.value?.length}个任务：筛选`
            : `${findValueInEnum(props.taskType, TaskTypeEnum)}任务-名单管理-添加呼叫：筛选`,
          params: params,
        })
        if (props.taskType === TaskTypeEnum['AI外呼']) {
          res = (filterType.value == 1 ? await aiOutboundTaskModel.batchFilterAiFinishedPhoneNum(params) : await aiOutboundTaskModel.getCallRecordAiNum(params)) as ResponseData
        } else {
          res = (filterType.value == 1 ? await aiOutboundTaskModel.batchFilterMixFinishedPhoneNum(params) : await aiOutboundTaskModel.getCallRecordMixNum(params)) as ResponseData
        }
        total.value = res?.total || 0
        stepNo.value = 1
      } catch(err) {
      }
      loading.value = false
    }
  })
}

const confirmTips = computed(() => {
  if (total.value === 0) return []
  const res:string[] = []
  if ((filterType.value === 1 && editData.putThroughNumRight !== 0) ||
  (filterType.value === 2 && editData.excludePutThrough != '1')) {
    res.push('包含【已接通】的数据，请确认符合业务要求')
  }
  if (filterType.value === 2 && callStatusArr.value?.includes(CallStatusEnum2['已屏蔽'])) {
    res.push('包含【已屏蔽】的数据，请确认原任务已取消屏蔽')
  }
  return res
})
// 筛选完成后，提交呼叫名单
const confirm = async () => {
  if (total.value <= 0) {
    return ElMessage({
      type: 'warning',
      message: '暂无数据添加！'
    })
  }
  loading.value = true
  editData.callStatus = (callStatusArr.value && callStatusArr.value.length > 0) ? callStatusArr.value.join(',') : ''
  emits('confirm', tableData.value?.map(item => {
    item.batchStatus = '执行中'
    return item
  }))
  const paramsOrigin = {
    ...pickAttrFromObj(editData, filterType.value === 2 ? [
      'phone', 'intentionLabelName', 'intentionLabelNameContains', 'whoHangup', 'calloutStartTime', 'calloutEndTime', 'calledNumLeft', 'calledNumRight', 'ifSendSms', 'sayCountLeft', 'sayCountRight',
      'cycleCountLeft', 'cycleCountRight', 'callDurationLeft', 'callDurationRight', 'operator', 'province', 'city', 'callStatus', 'ifFastRecall', 'excludePutThrough',
    ] : [
      'phone', 'operator', 'province', 'city', 'calledNumLeft', 'calledNumRight', 'putThroughNumLeft', 'putThroughNumRight',
      'addStartTime', 'addEndTime', 'lastCallStartTime', 'lastCallEndTime',
    ]),
    orderByTime: editData.orderByTime === 'none' ? undefined : editData.orderByTime,
  }
  await trace({
    page: !props.isSingle
      ? `外呼工具-批量添加呼叫-${tableData.value?.length}个任务：开始`
      : `${findValueInEnum(props.taskType, TaskTypeEnum)}任务-名单管理-添加呼叫：开始`,
    params: !props.isSingle ? paramsOrigin : {...paramsOrigin, taskId: tableData.value[0]?.id || ''},
  })
  stepNo.value = 2
  successNumber.value = 0
  failTable.value = []
  await Promise.all(tableData.value?.map(async (item, i) => {
    try {
      const params = {
        taskId: item.id,
        taskIds: item.id,
        ...paramsOrigin,
      }
      if (props.taskType === TaskTypeEnum['AI外呼']) {
        filterType.value == 1 ? await aiOutboundTaskModel.batchAddAiFinishedPhoneList(params) : await aiOutboundTaskModel.batchAddAiCallRecordList(params)
      } else {
        filterType.value == 1 ? await aiOutboundTaskModel.batchAddMixFinishedPhoneList(params) : await aiOutboundTaskModel.batchAddMixCallRecordList(params)
      }
      tableData.value[i]!.batchStatus = '执行成功'
      emits('confirm', tableData.value)
      successNumber.value ++ 
    } catch(err) {
      tableData.value[i]!.batchStatus = '执行失败'
      emits('confirm', tableData.value)
      failTable.value.push({
        ...item,
        remark: '失败'
      })
    }
  }))
  loading.value = false
  trace({
    page: !props.isSingle
      ? `外呼工具-批量添加呼叫-${tableData.value?.length}个任务：完成`
      : `${findValueInEnum(props.taskType, TaskTypeEnum)}任务-名单管理-添加呼叫：完成`,
    params: failTable.value?.length > 0 ? failTable.value.map(item => item.id).join(',').slice(0, 60) : null
  })
}
const exportFail = () => {
  emits('export', failTable.value, `批量添加【${filterType.value == 1 ? '名单管理' : '通话记录'}】失败.xlsx`)
}
const finishAction = () => {
  emits('confirmEnd')
  emits('update:visible', false)
}
const city = ref<string | undefined>(undefined)
const province = ref<string | undefined>(undefined)
watch(province, () => {
  editData.province = province.value?.split(',')[1] || undefined
  city.value = undefined
})
watch(city, () => {
  editData.city = city.value?.split(',')[1] || undefined
})
const provinceAllMap = ref<{ [key: string]: string[] }>({})
const cityList = computed(() => {
  return province.value ? (provinceAllMap.value[province.value] || []) : (Object.values(provinceAllMap.value).flat() || [])
})
const callStatusOptions = enum2Options(CallStatusEnum2)
const initData = () => {
  provinceAllMap.value = globalStore.provinceAllMap
  provinceList.value = globalStore.getProvinceList || []
  callStatusArr.value = [CallStatusEnum2['未接通']]
  stepNo.value = 0
  filterType.value = 1
  city.value = undefined
  province.value = undefined
  Object.assign(editData, new TaskCallOrigin())
}
watch(()=> props.visible, n => {
  visible.value = n
  if (n) {
    tableData.value = props.tableData || []
    initData()
  }
})

</script>

<style lang="postcss" type="text/postcss" scoped>
:deep(.el-input__wrapper) {
  width: 100%;
}
.normal-progress {
  :deep(.el-progress-bar__inner) {
    border-radius: 2px;
    background: linear-gradient(90deg, #0167FF 0%, #019FFF 100%);
  }
}
:deep(.el-step.is-simple .el-step__title) {
  font-size: var(--el-font-size-base);
}
.el-form {

  color: var(--primary-black-color-600);
  width: 100%;
  .el-form-item {
    margin-bottom: 14px;
  }
  :deep(.el-form-item__label) {
    padding-right: 0;
  }
  /* 默认的进度条为渐变蓝 */

}
</style>