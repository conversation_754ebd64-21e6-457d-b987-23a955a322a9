<template>
  <el-drawer
    v-model="visible"
    :before-close="closeDetails"
    :size="drawerWidth"
    :with-header="false"
  >
    <div class="clue-detail-container">
      <div class="tw-bg-white tw-w-full tw-flex tw-flex-col tw-flex-grow-0">
        <div class="tw-px-[16px] tw-w-full tw-flex tw-items-center tw-justify-between tw-py-[12px]">
          <span class="tw-text-[16px] tw-font-[600] tw-text-left tw-text-[var(--primary-black-color-600)]">线索详情</span>
          <el-button link @click="closeDetails">
            <el-icon :size="20" color="var(--primary-black-color-400)"><CloseBold /></el-icon>
          </el-button>
        </div>
      </div>
      <div class="card-box tw-mt-[16px]">
        <div class="tw-flex tw-flex-col tw-justify-between tw-flex-auto">
          <div class="tw-self-start tw-flex tw-items-center tw-h-[30px]">
            <span class="tw-text-[#313233] tw-text-[14px] tw-font-[600]">{{ clueData?.clueUniqueId || '-' }}</span>
            <!--显示跟进状态、审核状态和星标按钮-->
            <div class="tw-flex tw-flex-row tw-justify-start tw-items-center">
              <!--跟进状态-->
              <!--查看-->
              <!--线索管理、坐席组线索、坐席工作台，线索有跟进状态就可以查看-->
              <!--修改-->
              <!--线索管理、坐席组线索，已分配标签卡，已分配线索一定有跟进状态-->
              <!--坐席工作台，所有标签卡-->
              <el-dropdown
                v-show="clueData?.latestFollowUpStatus"
                :disabled="clueData?.latestFollowUpStatus && !permission['修改跟进状态']"
                trigger="click"
                @command="onConfirmChangeFollowUpStatus"
              >
                <div class="tw-flex tw-items-center">
                  <span class="status-box-mini tw-ml-[8px]" :class="getFollowStatusClass(clueData?.latestFollowUpStatus)">
                    {{findValueInEnum(clueData?.latestFollowUpStatus, FollowUpStatusEnum)}}
                  </span>
                  <el-icon v-if="clueData?.latestFollowUpStatus && permission['修改跟进状态']" color="#3377ff" class="tw-ml-[4px]" :size="14"><SvgIcon name="edit2"/></el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-for="followUpStatusItem in followUpStatusList"
                      :disabled="!!followUpStatusItem.disabled"
                      :key="followUpStatusItem.value"
                      :command="followUpStatusItem.value"
                      class="tw-flex tw-items-center tw-justify-between tw-w-[120px]"
                      :class="{'tw-bg-[#eaf0fe]': newFollowUpStatus==followUpStatusItem.value}"
                    >
                    <span class="status-box-mini" :class="!followUpStatusItem.disabled ? getFollowStatusClass(followUpStatusItem.value) : ''">
                      {{ followUpStatusItem.name }}
                    </span>
                    <el-icon v-if="newFollowUpStatus==followUpStatusItem.value" size="14" color="#165DFF"><SvgIcon name="gou" />  </el-icon>
                  </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <!--信息提示图标, 表单收集的表单校验通过时才显示跟进成功-->
              <!-- <el-tooltip v-if="(clueData?.latestFollowUpStatus && props.fromWorkbench && clueData.clueFollowUpLogIds?.length)" content="必须填写表单收集中的所有必填项，才能将状态更改为“跟进成功”。" placement="top" effect="dark">
                <el-icon class="tw-ml-[6px]" size="14">
                  <WarningFilled />
                </el-icon>
              </el-tooltip> -->

              <!-- 审核状态 -->
              <el-dropdown
                v-show="clueData?.latestFollowUpStatus=== FollowUpStatusEnum['跟进成功']"
                :disabled="!permission['审核线索']"
                trigger="click"
                @command="onConfirmChangeExamineStatus"
              >
                <div class="tw-flex tw-items-center">
                  <span class="status-box-mini tw-ml-[8px]" :class="getExamineStatusClass(clueData?.examineStatus)">
                    {{findValueInEnum(clueData?.examineStatus, ExamineStatusEnum) || '未审核'}}
                  </span>
                  <el-icon v-if="permission['审核线索']" color="#3377ff" class="tw-ml-[4px]" :size="14"><SvgIcon name="edit2"/></el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      v-for="examineStatusItem in enum2Options(ExamineStatusEnum)"
                      :key="examineStatusItem.value"
                      :command="examineStatusItem.value"
                      class="tw-flex tw-items-center tw-justify-between tw-w-[120px]"
                      :class="{'tw-bg-[#eaf0fe]': clueData?.examineStatus==examineStatusItem.value}"
                    >
                    <span class="status-box-mini" :class="getExamineStatusClass(examineStatusItem.value)">
                      {{ examineStatusItem.name }}
                    </span>
                    <el-icon v-if="clueData?.examineStatus==examineStatusItem.value" size="14" color="#165DFF"><SvgIcon name="gou" /></el-icon>
                  </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>

              <!--星标按钮-->
              <el-button v-show="permission['修改星标']" class="tw-ml-[6px]" link @click="onClickStar">
                <el-icon :size="20" color="#FFAA3D">
                  <SvgIcon v-if="clueData.star" name="star-active" />
                  <SvgIcon v-else name="star-inactive" />
                </el-icon>
              </el-button>
            </div>
          </div>
          <!-- 姓名、备注模块，支持编辑 -->
          <div v-loading="commentLoading" class="tw-flex tw-gap-x-1 tw-justify-start tw-self-start tw-items-center tw-w-full">
            <div class="tw-shrink-0 tw-grow-0 tw-flex tw-items-center">
              <span class="info-content tw-shrink-0">姓名：</span>
              <span v-if="!editCommentStatus" class="info-title-deep">
                {{ clueData?.name || '-' }}
              </span>
              <el-input
                v-else
                v-model="commentInfo.name"
                :maxlength="50"
                placeholder="请输入姓名，50字以内"
              >
              </el-input>
            </div>
            <div class="tw-text-left tw-flex tw-items-center tw-grow tw-relative">
              <span class="info-content tw-shrink-0">备注：</span>
              <el-scrollbar v-if="!editCommentStatus" max-height="60px" view-class="info-title-deep">
                {{ clueData?.comment || '-' }}
              </el-scrollbar>
              <el-input
                v-else
                class="tw-grow comment-area"
                type="textarea"
                show-word-limit
                :autosize="{maxRows: 3}"
                v-model="commentInfo.comment"
                :maxlength="500"
                placeholder="请输入备注，500字以内"
              >
              </el-input>
              <el-icon v-if="!editCommentStatus" color="#3377ff" class="tw-ml-[4px]" :size="14" @click="goCommentEdit"><SvgIcon name="edit2"/></el-icon>
              <div v-else class="tw-absolute tw-bottom-[-24px] tw-right-[10px]">
                <el-icon color="#3377ff" :size="16" @click="confirmCommentEdit"><Check /></el-icon>
                <el-icon color="#E54B17" class="tw-ml-[8px]" :size="16" @click="cancelCommentEdit"><Close/></el-icon>
              </div>
            </div>

          </div>
          <!-- 线索其他信息模块，状态、分配时间、跟进次数等 -->
          <div class="tw-flex tw-w-full">
            <div v-for="item in clueStatics" :key="item.name" class="tw-flex tw-items-center tw-mt-[12px] tw-px-[12px] tw-border-[#C8C9C] tw-border-r-[1px] first:tw-pl-0 last:tw-border-0">
              <span class="info-content">{{ item.name }}：</span>
              <span class="info-title-deep">
                {{ item.value }}
              </span>
            </div>
          </div>
        </div>
        <!--右上角按钮组-->
        <div v-if="props.fromWorkbench" class="tw-flex tw-flex-row tw-justify-end tw-items-center tw-self-start tw-flex-none tw-p-[4px] tw-bg-white">
          <!--人工直呼呼叫按钮-->
          <el-button link @click="onClickDirectCall">
            <el-icon :size="52" color="#fff">
              <SvgIcon name="dial-green" />
            </el-icon>
          </el-button>
          <!--发送短信按钮-->
          <el-button link @click="onClickSendSms">
            <el-icon :size="46" color="#fff">
              <SvgIcon name="sms-send" />
            </el-icon>
          </el-button>
        </div>
      </div>
      <el-scrollbar class="tw-flex-grow tw-mt-[12px]" wrap-class="tw-px-[12px]" ref="clueTitleRef">
        <!-- 表单信息模块 -->
        <div v-loading="formLoading" class="card-box tw-flex-col" id="form-dom" :class="!isFormExtend ? 'tw-max-h-[140px]':''">
          <div class="tw-mb-[12px] tw-flex tw-justify-between tw-items-center tw-w-full">
            <span class="title tw-text-left">表单收集</span>
            <div v-if="permission['编辑表单']">
              <el-button v-if="!formEditStatus" type="primary" link @click="formEditStatus=true">
                <el-icon><SvgIcon name="edit2"/></el-icon>
                <span>编辑</span>
              </el-button>
              <template v-else>
                <el-button type="primary" link @click="updateFormRecord">
                  <el-icon><SvgIcon name="edit2"/></el-icon>
                  <span>保存</span>
                </el-button>
                <el-button type="primary" link @click="formEditStatus=false" :icon="Close">
                  <span>取消</span>
                </el-button>
              </template>
            </div>
          </div>
          <el-scrollbar class="tw-w-full" max-height="300px" wrap-class="tw-pr-[12px]">
            <FormRecord v-if="formRecordData.fromCollectionContentList?.length > 0 || formEditStatus" :gridNum="2" v-model:formRecordContent="formRecordData.fromCollectionContentList" :formSetting="formEnableSetting||[]" :readonly="!formEditStatus"/>
            <span v-else="!formRecordData.fromCollectionContentList || formRecordData.fromCollectionContentList?.length === 0" class="info-title">暂无数据</span>
          </el-scrollbar >
        </div>
        <div v-if="formRecordData.fromCollectionContentList?.length > 2" class="tw-flex tw-justify-center">
          <div class="trapezoid" @click="isFormExtend=!isFormExtend">
            <el-icon v-if="isFormExtend" :size="13"><ArrowUpBold/>  </el-icon>
            <el-icon v-else :size="13"><ArrowDownBold/>  </el-icon>
          </div>
        </div>

        <!-- 标签模块：跟进、流转、AI跟进记录 -->
        <TabsBox
          id="data-dom"
          v-model:active="activeTab"
          :tabList="tabList"
          class="tw-sticky tw-top-0 tw-left-0 tw-z-[2]"
          @update:active="handleTabChange"
          :class="formRecordData.fromCollectionContentList?.length > 0 ? 'tw-mt-[8px]' : 'tw-mt-[12px]'"
        ></TabsBox>
        <div v-if="['跟进记录', 'AI跟进记录'].includes(activeTab)" v-loading="followUpLoading" class="tw-bg-[#f2f3f5] tw-sticky tw-pt-0 tw-flex tw-h-[calc(100vh-230px)]">
          <el-scrollbar v-if="activeTab=='跟进记录' && followLogList&&followLogList?.length>0" max-height="100%" wrap-class="tw-px-[12px]" class="tw-bg-white tw-border-r-[1px] tw-flex-grow-0 tw-flex-shrink-0 ">
            <TimeLineBox
              :list="followLogList.map(item => item.clueFollowUpLog)"
              :active="activeFollowLog?.id"
              @update:active="handleActiveFollowLog"
              activeProps="id"
              sortProps="followUpTime"
              :gap="12"
            >
              <template v-slot:content="{row}:{row:ClueFollowLog}">
                <div
                  class="tw-grid tw-grid-cols-2 tw-gap-[12px] tw-box-border
                  tw-text-left tw-bg-[#F5F7FA] tw-w-[347px]
                  tw-p-[16px] tw-rounded-[4px] tw-border-[1px] tw-mt-[12px]"
                  :class="row?.id==activeFollowLog?.id ? 'tw-border-[#165DFF]': 'tw-border-[#F5F7FA]'"
                >
                  <div class="tw-flex tw-flex-col">
                    <span class="info-title">跟进时间：</span>
                    <span class="info-title-deep tw-mt-[4px]">{{ row?.followUpTime ? dayjs(row?.followUpTime).format('YYYY-MM-DD HH:mm:ss') : '' }}</span>
                  </div>
                  <div class="tw-flex tw-flex-col">
                    <span class="info-title">跟进状态：</span>
                    <span class="info-title-deep tw-mt-[4px]">{{ findValueInEnum(row?.followUpStatus, FollowUpStatusEnum) || '-' }}</span>
                  </div>
                  <div class="tw-flex tw-flex-col">
                    <span class="info-title">跟进坐席： </span>
                    <span class="info-title-deep tw-mt-[4px]">{{ callSeatList?.find(item => item.id ===row?.callSeatId)?.account || '-' }}</span>
                  </div>
                  <div class="tw-flex tw-flex-col">
                    <span class="info-title">跟进类型：</span>
                    <span class="info-title-deep tw-mt-[4px]">{{ findValueInEnum(row?.followUpType, FollowUpTypeEnum) || '-' }}</span>
                  </div>
                  <div class="tw-flex tw-flex-col" v-if="row?.followUpType !== FollowUpTypeEnum['直发短信']">
                    <span class="info-title">通话时长：</span>
                    <span class="info-title-deep tw-mt-[4px]">{{ (row?.callDuration??-1)>-1 ? formatDuration((row?.callDuration??0)/1000)??'-' : '-' }}</span>
                  </div>

                  <template v-if="row?.followUpType === FollowUpTypeEnum['直发短信']">
                    <div class="tw-flex tw-flex-col">
                      <span class="info-title">短信状态：</span>
                      <span class="info-title-deep tw-mt-[4px]">
                        {{
                          findValueInEnum((followLogList.find((item: ClueFollowItem) => {
                            return item?.clueFollowUpLog?.id === row?.id
                          }) ?? {})?.smsRecord?.smsStatus, SmsStatusEnum) || '-'
                        }}
                      </span>
                    </div>
                    <el-button link type="primary" @click="onClickViewSmsContent(row)">
                      查看
                    </el-button>
                  </template>
                  <template v-else>
                    <div class="tw-flex tw-flex-col">
                      <span class="info-title">下次跟进时间：</span>
                      <span class="info-title-deep tw-mt-[4px]">
                        {{ row?.nextFollowUpTime ? dayjs(row?.nextFollowUpTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
                      </span>
                    </div>
                  </template>

                  <div class="tw-col-span-2">
                    <span class="info-title">跟进备注：</span>
                    <span class="info-title-deep">{{ row?.note || '-' }}</span>
                  </div>
                </div>
              </template>
            </TimeLineBox>
          </el-scrollbar>
          <el-tabs v-if="activeTab=='AI跟进记录'" v-model="activeInnerTab" type="border-card" tabPosition="left" @tab-change="handleInnerTabChange">
            <el-tab-pane label="今日" name="今日"></el-tab-pane>
            <el-tab-pane label="历史" name="历史"></el-tab-pane>
          </el-tabs>
          <div v-if="currentRecordInfo && currentRecordInfo?.id" class="tw-flex-grow tw-flex-shrink tw-overflow-hidden">
            <div class="tw-flex tw-items-center tw-p-[12px] tw-bg-white tw-flex-grow-0 tw-flex-shrink-0">
              <span class="tw-mr-1 tw-flex-shrink-0">通话录音</span>
              <AudioMode
                v-if="currentRecordInfo && currentRecordInfo.callId && currentRecordInfo.wholeAudioFileUrl"
                :audioUrl="currentRecordInfo.wholeAudioFileUrl || ''"
                :audioName="currentRecordInfo.taskName || '未知任务'"
                v-model:audioStatus="audioStatus"
                v-model:audioVolume="audioVolume"
              >
              </AudioMode>
            </div>
            <div class="tw-flex tw-h-[calc(100%-70px)]">
              <div v-if="isRecordExtend" class="tw-flex-grow-0 tw-flex-shrink-0 tw-h-full tw-w-[265px] tw-bg-white">
                <CallRecordInfoBox
                  :record-data="currentRecordInfo!"
                  :record-type="recordType"
                  :show-plaintext="permission['查看明文']"
                  @updateRecord="updateRecordLeftInfo"
                  :readonly="true"
                />
              </div>
              <div class="tw-flex-grow-0 tw-flex-shrink-0 tw-self-center">
                <div class="trapezoid-col" @click="isRecordExtend=!isRecordExtend">
                  <el-icon v-if="isRecordExtend" :size="13"><ArrowLeftBold/>  </el-icon>
                  <el-icon v-else :size="13"><ArrowRightBold/>  </el-icon>
                </div>
              </div>
              <div class="tw-flex-grow tw-flex-shrink">
                <CallRecordDialogBoxNew
                  v-loading="dialogLoading"
                  v-model:needUpdate="needUpdate"
                  v-model:clearAudio="clearAudio"
                  :callRecordData="currentRecordInfo!"
                  :dataList="dialogData"
                />
              </div>
            </div>
          </div>
          <el-empty v-else class="tw-grow"></el-empty>
        </div>
        <el-scrollbar v-if="activeTab=='流转记录' && clueTransferRecordList?.length>0 && props.transferRecordVisible" v-loading="followUpLoading" max-height="calc(100vh-230px)" wrap-class="tw-px-[24px] tw-py-[12px]" class="tw-bg-white">
          <TimeLineBox
            :list="clueTransferRecordList"
            :active="-1"
            activeProps="id"
            :needLastTimeLine="false"
            sortProps="operationTime"
            :gap="4"
          >
            <template v-slot:content="{row}">
              <div class="tw-pb-[8px]">
                <span class="info-title-deep tw-mr-[18px]">{{ row?.operationTime ? dayjs(row?.operationTime).format('HH:mm:ss') : '' }}</span>
                <span class="tw-text-[#165DFF] tw-mr-[6px]">{{ row?.operateAccount }}</span>
                <span class="info-title">进行了</span>
                <span class="info-title-deep tw-font-[600] tw-mr-[4px] tw-ml-[2px]">{{ findValueInEnum(row?.operationType, OperateTypeEnum) || '-' }}</span>
                <span class="info-title">操作</span>
              </div>
            </template>
          </TimeLineBox>
        </el-scrollbar>
        <el-empty v-if="showEmpty" class="tw-bg-[#fff]"></el-empty>
      </el-scrollbar>
    </div>
    <!-- <FormRecordDialog v-if="formEditStatus" v-model:visible="formEditStatus" :formSetting="formEnableSetting" :formRecord="formRecordData" @confirm="updateFormRecord"/> -->
  </el-drawer>

  <!--发送短信弹窗-->
  <SendSmsDialog v-if="seatPage === SeatPageEnum.CLUE" />

  <!--短信预览弹窗-->
  <SmsPreviewDialog v-model:visible="smsPreviewDialogVisible" :content="smsPreviewDialogContent"/>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'
import { useTaskStore } from '@/store/taskInfo'
import { useUserStore } from '@/store/user'
import { computed, defineAsyncComponent, onDeactivated, onUnmounted, reactive, ref, watch } from 'vue'
import {
  ArrowDownBold,
  ArrowLeftBold,
  ArrowRightBold,
  ArrowUpBold,
  Check,
  Close,
  CloseBold,
} from '@element-plus/icons-vue'
import { enum2Options, findValueInEnum, formatDuration } from '@/utils/utils'
import { trace } from '@/utils/trace'
import { SeatLogActionEnum, SeatLogTypeEnum, SeatMember, SeatPageEnum, SeatTeam } from '@/type/seat'
import { SpeechCraftInfoItem } from '@/type/speech-craft'
import {
  ClueFollowItem,
  ClueFollowLog,
  ClueItem,
  ClueStatusEnum,
  ClueTransferRecordItem,
  CollectionFormItem,
  ExamineStatusEnum,
  FollowUpStatusEnum,
  FollowUpTypeEnum,
  FormRecordItem,
  OperateTypeEnum,
} from '@/type/clue'
import { RecordDialogueData, RecordTypeEnum, TaskCallRecordItem } from '@/type/task'
import { getExamineStatusClass, getFollowStatusClass, } from './constant'
import { aiOutboundTaskModel } from '@/api/ai-report'
import { clueManagerModel, formRecordModel } from '@/api/clue'
import TabsBox from '@/components/TabsBox.vue'
import { seatCallRecordModel, seatWorkbenchClueModel } from '@/api/seat'
import { useSeatPhoneStore } from '@/store/seat-phone'
import to from 'await-to-js';
import { onBeforeRouteLeave } from 'vue-router'
import { ResponseData } from '@/axios/request/types'
import { useSmsStore } from '@/store/sms'
import SendSmsDialog from '@/components/sms/SendSmsDialog.vue'
import { SmsStatusEnum } from '@/type/sms'
import SmsPreviewDialog from '@/components/sms/SmsPreviewDialog.vue'
import { useSeatInfoStore } from '@/store/seat/seat-info'
import { storeToRefs } from 'pinia'

const FormRecord = defineAsyncComponent({loader: () => import('@/components/clue/FormRecord.vue')})
const AudioMode = defineAsyncComponent({loader: () => import('@/components/AudioMode.vue')})
const CallRecordInfoBox = defineAsyncComponent({loader: () => import('@/components/record/CallRecordInfoBox.vue')})
const CallRecordDialogBoxNew = defineAsyncComponent({loader: () => import('@/components/record/CallRecordDialogBoxNew.vue')})
const TimeLineBox = defineAsyncComponent({loader: () => import('@/components/TimeLineBox.vue')})

const props = withDefaults(defineProps<{
  visible: boolean; // 抽屉的可见性
  clueData: ClueItem; // 线索信息
  // 是否【审核线索，编辑表单，修改跟进状态，修改星标, 查看明文】的权限（务必确保中文和前面的枚举一致）
  permission?: Record<string, boolean>;
  transferRecordVisible?: boolean; // 是否显示流转记录模块
  fromWorkbench?: boolean; // 父组件是否来自坐席工作台
}>(),{
  transferRecordVisible: true,
  fromWorkbench: false,
})
const emits = defineEmits(['close', 'update:visible', 'update:data'])

// 读取props数据，并用监听器监听变化
const visible = ref(props.visible || false)
const needUpdateList = ref(false) // 关闭抽屉后是否需要更新列表
const formEditStatus = ref(false) // 表单收集处于：只读/false；编辑/true
const permission = computed(() => props.permission || {}) // 是否【审核线索，编辑表单，修改跟进状态，修改星标, 查看明文】的权限

// pinia数据读取
const taskStore = useTaskStore()
const seatPhoneStore = useSeatPhoneStore()
const userInfo = useUserStore()
const smsStore = useSmsStore()
const seatInfoStore = useSeatInfoStore()
const { currentSeat, seatPage } = storeToRefs(seatInfoStore)

// 详情抽屉宽度，监听resize实现调整
const drawerWidth = ref(window.innerWidth > 1400 ? '75%' : '950px')
const getDrawerWidth = () => {
  drawerWidth.value = window.innerWidth > 1400 ? '75%' : '950px'
}
const showEmpty = computed(() => {
  return (activeTab.value === '跟进记录' && (!followLogList.value ||followLogList.value?.length === 0))
  || (activeTab.value === '流转记录' && (!clueTransferRecordList.value || clueTransferRecordList.value?.length === 0) && props.transferRecordVisible)
})
// 抽屉关闭
const closeDetails = () => {
  window.removeEventListener('resize', getDrawerWidth)
  emits('update:visible', false)
  needUpdateList.value && emits('update:data')
}

// 线索数据
const clueData = reactive<ClueItem>(props.clueData) // 入参，线索数据

/** 模块1： 基础信息 */
const clueStatics = ref<{
  name: string, value?: string | number
}[]>([]) // 顶部展示数据，从线索数据抽离出来成列表

/** 编辑姓名、备注 开始 */
const commentLoading = ref(false)
const editCommentStatus = ref(false)
const commentInfo = reactive({
  id: -1,
  name: '',
  comment: ''
})
const goCommentEdit = () => {
  if (clueData.id && clueData.id > 0) {
    editCommentStatus.value = true
    Object.assign(commentInfo, {
      id: clueData.id,
      name: clueData.name,
      comment: clueData.comment,
    })
  } else {
    ElMessage.warning('获取线索ID失败')
  }
}
const confirmCommentEdit = async () => {
  if (commentInfo.id && commentInfo.id > 0) {
    commentLoading.value = true
    const [err, res] = await to(clueManagerModel.updateClueComment(commentInfo))
    if (!err) {
      ElMessage.success('操作成功')
      emits('update:data')
      editCommentStatus.value = false
      Object.assign(clueData, {
        name: commentInfo.name,
        comment: commentInfo.comment,
      })
    }
    commentLoading.value = false
  }
}
const cancelCommentEdit = () => {
  editCommentStatus.value = false
}
/** 编辑姓名、备注 结束 */

// 根据线索状态，显示线索时间信息
// const clueTimeInfo = computed(() => {
//   switch(clueData?.clueStatus) {
//     case ClueStatusEnum['待下发']: return `导入时间：${clueData?.importTime ? dayjs(clueData?.importTime).format('YYYY-MM-DD HH:mm:ss'):'-'}`;
//     case ClueStatusEnum['待分配']: return `下发时间：${clueData?.beSendTime ? dayjs(clueData?.beSendTime).format('YYYY-MM-DD HH:mm:ss'):'-'}`;
//     case ClueStatusEnum['已分配']: return `分配时间：${clueData?.beAllocatedTime ? dayjs(clueData?.beAllocatedTime).format('YYYY-MM-DD HH:mm:ss'):'-'}`;
//     case ClueStatusEnum['已回收']: return `回收时间：${clueData?.recoveryTime ? dayjs(clueData?.recoveryTime).format('YYYY-MM-DD HH:mm:ss'):'-'}`;
//     case ClueStatusEnum['已归档']: return `归档时间：${clueData?.archivedTime ? dayjs(clueData?.archivedTime).format('YYYY-MM-DD HH:mm:ss'):'-'}`;
//     default: return `导入时间：${clueData?.importTime ? dayjs(clueData?.importTime).format('YYYY-MM-DD HH:mm:ss'):'-'}`;
//   }
// })

/** 模块2：表单收集部分 开始 */
class FormRecordOriginItem {
  callSeatId = undefined
  clueId = undefined
  formRecordId = undefined
  fromCollectionContentList = []
}
const formRecordData = reactive<FormRecordItem>(new FormRecordOriginItem())
const formEnableSetting = ref<CollectionFormItem[] | null>([])
const isFormExtend = ref(true) // 是否展开表单收集
const formLoading = ref(false)
const updateFormRecord = async () => {
  formLoading.value = true
  const [err, data1] = await to(formRecordModel.saveFormRecord(formRecordData))
  formLoading.value = false
  if (err) ElMessage.error('操作失败')
  ElMessage({
    type: 'success',
    message: '操作成功'
  })
  const [_, data] = await to(formRecordModel.getFormRecordByClueId({
    clueId: clueData?.id!
  }))

  validFormRecord()

  Object.assign(formRecordData, data, {clueId: clueData?.id!})
  formEditStatus.value = false
}
/** 模块2：表单收集部分 结束 */

/** 模块3： 数据记录（跟进+流转） */
let tabList = ['跟进记录', '流转记录', 'AI跟进记录']
const activeTab = ref('跟进记录')
/** 模块3-1： 跟进记录-列表 */
const followLogList = ref<ClueFollowItem[] | null>([]) // 跟进记录列表
const activeFollowLog = reactive<ClueFollowLog>({}) // 当前激活的跟进记录，展开其通话记录详情
const currentRecordInfo = ref<TaskCallRecordItem | null>({})
// 点击切换跟进记录
const handleActiveFollowLog = async (item: ClueFollowLog) => {
  Object.assign(activeFollowLog, item)
  const row = followLogList.value?.find(v => v.clueFollowUpLog.id === item.id)
  if (row?.clueFollowUpLog?.id) {
    currentRecordInfo.value = (row.callRecordForHumanMachine && {...row.callRecordForHumanMachine}) || (row.callRecordForManualDirect && {...row.callRecordForManualDirect}) || {}
  }
  updateRecordData()
}
/** 模块3-1： 跟进记录-通话详情 */
const isRecordExtend = ref(true) // 是否展开记录，左侧的详情
// 通话详情右侧
// 播放录音
const audioStatus = ref<'pause' | 'play' | 'none'>('pause') // 左侧通话记录音频播放状态
const audioVolume = ref<number>(70) // 左侧通话记录音频声音
const clearAudio = ref(false) // 对于一些操作，需要是的对话组件的音频清空

const dialogLoading = ref(false) // 针对右侧对话部分的的loading
const dialogData = ref<RecordDialogueData[]>([]) // 对话数据列表
const needUpdate = ref(false)
const recordType = computed(() => {
  if (activeTab.value === '跟进记录') {
    return activeFollowLog?.followUpType === FollowUpTypeEnum['人机协同'] ? RecordTypeEnum['人机协同'] : RecordTypeEnum['人工直呼']
  } else {
    return RecordTypeEnum['AI外呼']
  }
})
/** 更新机器人用户对话数据 */
const updateRecordData = async () => {
    dialogLoading.value = true
    const callId = currentRecordInfo.value?.callId || ''
    const recordId = currentRecordInfo.value?.recordId || ''
    if (!callId) {
      dialogData.value = []
      needUpdate.value = true
      dialogLoading.value = false
      return
    }
    if (activeTab.value === '跟进记录') {
      switch(activeFollowLog?.followUpType) {
        case FollowUpTypeEnum['人机协同']: {
          const [_, data] = await to(seatCallRecordModel.getMixDialogueDataList({
            callId: callId,
            recordId: recordId,
          }))
          dialogData.value = data || []
          break;
        }
        case FollowUpTypeEnum['人工直呼']: {
          const [_, data] = await to(seatCallRecordModel.getManualDialogueDataList({
            callId: callId,
            recordId: recordId,
          }))
          dialogData.value = data || []
          break;
        }
        default : {
          const [_, data] = await to(seatCallRecordModel.getMixDialogueDataList({
            callId: callId,
            recordId: recordId,
          }))
          dialogData.value = data || []
          break;
        }
      }
    }
    if (activeTab.value === 'AI跟进记录') {
      const [err1, data1] = await to(aiOutboundTaskModel.getAiDialogueDataList({
        callId: callId,
        recordId: recordId,
      }))
      dialogData.value = data1 || []
    }
    dialogLoading.value = false
    needUpdate.value = true
}
const updateRecordLeftInfo = async () => {
  if (!clueData.id) {
    return ElMessage.warning('获取线索ID失败')
  }
  followLogList.value = await seatWorkbenchClueModel.getFollowById({clueId: clueData.id!}) || []
  const row = followLogList.value.find(item => item.clueFollowUpLog.id === activeFollowLog.id)
  if (row?.clueFollowUpLog?.id) {
    Object.assign(activeFollowLog, row.clueFollowUpLog)
    currentRecordInfo.value = (row.callRecordForHumanMachine && {...row.callRecordForHumanMachine}) || (row.callRecordForManualDirect && {...row.callRecordForManualDirect}) || {}
    updateRecordData()
  } else {
    currentRecordInfo.value = null
    dialogData.value = []
  }
}
/** 模块3-2： 流转记录 */
const clueTransferRecordList = ref<ClueTransferRecordItem[]>([]) // 流转记录
const updateTransferRecord = async () => {
  // 流转记录
  if (props.transferRecordVisible && clueData.clueTransferRecordIds && clueData.clueTransferRecordIds.length > 0) {
    clueTransferRecordList.value = await clueManagerModel.getTransferRecordById(clueData.clueTransferRecordIds) || []
  } else {
    clueTransferRecordList.value = []
  }
}
/** 模块3-3：ai跟进记录 */
const activeInnerTab = ref('今日')
const updateAiRecord = () => {
  activeInnerTab.value = '今日'
  handleInnerTabChange('今日')
}
const handleInnerTabChange = async (tabName: string) => {
  if (!clueData.phone) return ElMessage.warning('未获取到线索手机号')
  followUpLoading.value = true
  const [_, res] = await to(aiOutboundTaskModel.findFollowCallRecordList({
    phone: clueData.phone,
    account: userInfo.account,
    callStatus: '7',
    calloutStartTime: tabName == '今日' ? dayjs().startOf('day').format('YYYY-MM-DD HH:mm:ss') : dayjs().add(-14, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss'),
    calloutEndTime: tabName == '今日' ? dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss') : dayjs().add(-1, 'day').endOf('day').format('YYYY-MM-DD HH:mm:ss'),
  })) as [any, ResponseData];
  const arr = (res.data as TaskCallRecordItem[] || []).sort((a, b) => dayjs(a.callOutTime).isAfter(dayjs(b.callOutTime)) ? -1 : 1)
  if (arr && arr?.length > 0) {
    currentRecordInfo.value = arr[0] || null
    updateRecordData()
  } else {
    currentRecordInfo.value = null
    dialogData.value = []
  }
  followUpLoading.value = false
}

const handleTabChange = (val: string) => {
  switch(val) {
    case '跟进记录': updateRecordLeftInfo();break;
    case '流转记录': updateTransferRecord();break;
    case 'AI跟进记录': updateAiRecord();break;
  }
}

// 初始化数据：获取数据，翻译需要的列表
const callTeamList = ref<SeatTeam[] | null>([])
const callSeatList = ref<SeatMember[] | null>([])
const speechCraftList = ref<SpeechCraftInfoItem[] | null>([])
const followUpLoading = ref(false)
const init = async () =>  {
  // 是否允许显示流转记录
  tabList = props.transferRecordVisible ? ['跟进记录', '流转记录', 'AI跟进记录'] : ['跟进记录', 'AI跟进记录']
  activeTab.value = tabList[0]!
  callTeamList.value = await taskStore.getCallTeamListOptions()
  callSeatList.value = await taskStore.getCallGroupSeatListOptions()
  speechCraftList.value = await taskStore.getAllScriptListOptions()
  // 获取最新表单设置
  formEnableSetting.value = await taskStore.getEnableFormSetting()
  if (props.clueData?.id && props.clueData?.id > 0) {
    Object.assign(clueData, props.clueData)
    clueStatics.value = [
      {name: '线索状态', value: findValueInEnum(clueData?.clueStatus, ClueStatusEnum)||'-'},
      {name: '分配时间', value: clueData?.beAllocatedTime ? dayjs(clueData?.beAllocatedTime).format('YYYY-MM-DD HH:mm:ss'):'-'},
      {name: '跟进次数', value: clueData?.followCount ?? '-'},
      {name: '最近跟进时间', value: clueData?.latestFollowUpTime ? dayjs(clueData?.latestFollowUpTime).format('YYYY-MM-DD HH:mm:ss') : '-'},
    ]
    // 表单信息
    const [_, data] = await to(formRecordModel.getFormRecordByClueId({
      clueId: props.clueData?.id!
    }))
    Object.assign(formRecordData, data, {clueId: props.clueData?.id})
    // 跟进记录
    followUpLoading.value = true
    const [err, res] = await to(seatWorkbenchClueModel.getFollowById({clueId: clueData.id!}))
    followLogList.value = res ? res?.sort((a,b) => dayjs(a.clueFollowUpLog.followUpTime).isAfter(dayjs(b.clueFollowUpLog.followUpTime)) ? -1 : 1) : []
    if (followLogList.value[0] ) {
      handleActiveFollowLog(followLogList.value[0].clueFollowUpLog)
    } else {
      currentRecordInfo.value = null
      dialogData.value = []
    }

    followUpLoading.value = false
  }

  // 编辑备注状态初始化
  editCommentStatus.value = false

  // 校验表单收集内容，更新跟进状态可选列表
  validFormRecord()
  // 暂时无需更新列表
  needUpdateList.value = false

  let timer: any = setTimeout(() => {
    clueTitleRef.value?.scrollTo({top: 0})
    timer && clearTimeout(timer)
    timer = null
  }, 200)
}
const clueTitleRef = ref()

// 待提交的新的跟进状态
const newFollowUpStatus = ref(FollowUpStatusEnum['跟进中'])
// 跟进状态可选列表
const followUpStatusList = ref<{ name: string, value: any, disabled?: boolean }[]>([])

/**
 * 更新跟进状态可选列表
 * @param {boolean} allowSuccess 允许选择跟进成功
 */
const updateFollowUpStatusList = (allowSuccess: boolean = false) => {
  if (allowSuccess) {
    followUpStatusList.value = [
      { name: '跟进中', value: FollowUpStatusEnum['跟进中'] },
      { name: '跟进成功', value: FollowUpStatusEnum['跟进成功'] },
      { name: '跟进失败', value: FollowUpStatusEnum['跟进失败'] },
    ]
  } else {
    // 如果当前状态是跟进成功，则应该立马修改成跟进中
    if (newFollowUpStatus.value === FollowUpStatusEnum['跟进成功']) {
      newFollowUpStatus.value = FollowUpStatusEnum['跟进中']
    }
    followUpStatusList.value = [
      { name: '跟进中', value: FollowUpStatusEnum['跟进中'] },
      { name: '跟进失败', value: FollowUpStatusEnum['跟进失败'] },
      { name: '跟进成功', value: FollowUpStatusEnum['跟进成功'], disabled: true },
    ]
  }
}

/**
 * 表单收集校验
 */
const validFormRecord = () => {
  interface FormRecordItemContent {
    id?: number,
    formCollectionId: number,
    content: string
  }

  // 根据表单设置（数据结构）校验必要项是否已填写
  const result: boolean = (formEnableSetting.value || []).every((settingItem: CollectionFormItem) => {
    // 必要
    if (settingItem?.requiredField) {
      const item = formRecordData?.fromCollectionContentList?.find((contentItem: FormRecordItemContent) => {
        return contentItem?.formCollectionId === settingItem?.id
      })
      // 表单项内容为空，返回false，否则返回true
      return !!item?.content
    }
    // 非必要
    return true
  })

  // 更新跟进状态可选列表
  updateFollowUpStatusList(result)
}
/**
 * 点击确定修改跟进状态按钮
 */
const onConfirmChangeFollowUpStatus = async (val: FollowUpStatusEnum) => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '线索详情 点击确定修改跟进状态按钮',
  })
  newFollowUpStatus.value = val
  // 处理参数
  const params = {
    callSeatId: currentSeat.value.id ?? -1,
    clueId: props.clueData.id ?? -1,
    followUpLogId: props.clueData.clueFollowUpLogIds?.at(-1) ?? -1,
    status: newFollowUpStatus.value ?? ''
  }

  // 请求接口
  const [err] = await to(seatWorkbenchClueModel.changeFollowUpStatus(params))
  trace({
    page: `线索管理-修改跟进状态（${location.href || ''}）`,
    params: params
  })

  needUpdateList.value = true
  emits('update:data')
  if (!err) {
    ElMessage.success('修改成功')
    // 更新线索的跟进状态
    clueData.latestFollowUpStatus = newFollowUpStatus.value
    !clueData.examineStatus && (clueData.examineStatus = ExamineStatusEnum['未审核'])
  }
}

/**
 * 点击确定修改审核状态按钮
 */
const onConfirmChangeExamineStatus = async (val: ExamineStatusEnum) => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '线索详情 点击确定修改审核状态按钮',
  })
  const params = {
    clueId: props.clueData.id ?? -1,
    examineStatus: val
  }
  const [err] = await to(clueManagerModel.setClueExamineStatus(params))
  trace({
    page: `线索管理-审核线索（${location.href || ''}）`,
    params: params
  })
  if (!err) {
    clueData.examineStatus = val
    ElMessage.success('修改成功')
    needUpdateList.value = true
  }
}

/**
 * 点击星标按钮
 */
const onClickStar = async () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '线索详情 点击星标按钮',
  })
  const params = {
    clueId: clueData.id!,
    isStar: !clueData.star,
  }
  const [err] = await to(seatWorkbenchClueModel.switchClueStar(params))
  trace({
    page: `线索管理-修改星标（${location.href || ''}）`,
    params: params
  })
  if (!err) {
    clueData.star = !clueData.star
  }
  needUpdateList.value = true
}
/**
 * 点击人工直呼呼叫按钮
 */
const onClickDirectCall = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '线索详情 点击人工直呼呼叫按钮',
  })
  closeDetails()
  seatPhoneStore.launchCall(clueData)
}
/**
 * 点击发送短信按钮
 */
const onClickSendSms = () => {
  seatPhoneStore.report({
    type: SeatLogTypeEnum['详细'],
    action: SeatLogActionEnum['坐席页面交互'],
    desc: '线索详情 点击发送短信按钮',
  })
  // 接口参数
  smsStore.sendSmsDialogForm.clueId = clueData.id
  // 页面展示
  smsStore.sendSmsDialogForm.phoneNumber = clueData.clueUniqueId
  // 组件数据
  smsStore.sendSmsDialogForm.type = FollowUpTypeEnum['直发短信']
  // 显示弹窗
  smsStore.showSendSmsDialog()
}
// 短信预览弹窗 是否显示
const smsPreviewDialogVisible = ref<boolean>(false)
// 短信预览弹窗 短信内容
const smsPreviewDialogContent = ref<string>('')
/**
 * 点击查看短信按钮
 * @param log 跟进记录数据
 */
const onClickViewSmsContent = (log: ClueFollowLog) => {
  // 用跟进记录ID查找出整个跟进记录信息，然后得到短信内容
  const item: ClueFollowItem | undefined = followLogList.value?.find((item: ClueFollowItem) => {
    return item?.clueFollowUpLog.id === log.id
  })
  if (item?.smsRecord) {
    smsPreviewDialogContent.value = item.smsRecord?.smsContent ?? ''
    smsPreviewDialogVisible.value = true
  }
}

watch([() => props.visible, () => props.clueData?.id ], () => {
  visible.value = props.visible
  if (props.visible) {
    init()
    window.addEventListener('resize', getDrawerWidth)
  } else {
    window.removeEventListener('resize', getDrawerWidth)
  }
})

const clearAll = () => {
  callTeamList.value = null
  callSeatList.value = null
  speechCraftList.value = null
  clueTitleRef.value = null
  Object.assign(formRecordData, new FormRecordOriginItem())
  formEnableSetting.value = null
  followLogList.value = null
  currentRecordInfo.value = null
  window.removeEventListener('resize', getDrawerWidth)
}
onUnmounted(() => {
  clearAll()
})
onDeactivated(() => {
  clearAll()
})
onBeforeRouteLeave(() => {
  clearAll()
})
</script>

<style lang="postcss" scoped type="text/postcss">
.clue-detail-container {
  box-sizing: border-box;
  font-size: 13px;
  height: calc(100% - 60px);
  background-color: #f2f3f5;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  .trapezoid-col {
    display: flex;
    justify-content: center;
    align-items: center;
    align-self: center;
    width: 15px;
    height: 95px;
    padding-bottom: 3px;
    background: #fff;
    cursor: pointer;
    clip-path: polygon(0 0, 0 100%, 100% 82%, 100% 18%);
  }

}
:deep(.comment-area .el-textarea__inner){
  padding-bottom: 6px;
}
:deep(.el-dropdown-menu__item) {
  margin: 2px 4px;
  padding: 4px 8px;
  border-radius: 4px;
}
.el-tabs--border-card {
  width: 100px;
  :deep(.el-tabs__header .is-left) {
    width: 100px;
  }
  :deep(.el-tabs__nav .is-left) {
    width: 100%;
    display: flex;
    justify-content: center;
  }
}
</style>
