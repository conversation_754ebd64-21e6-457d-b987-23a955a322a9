<template>
  <div class="line-manger-container">
    <div class="tw-w-full tw-bg-white tw-p-[16px] tw-pb-[8px] tw-grow-0 tw-shrink-0">
      <div class="tw-grid tw-grid-cols-5 2xl:tw-grid-cols-7 tw-gap-3 tw-border-b-[1px] tw-pb-[12px]">
        <div class="item">
          <el-input
            v-model.trim="searchForm.supplyLineName"
            placeholder="供应线路名称"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.supplyLineNumber"
            placeholder="供应线路编号"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.masterCallNumber"
            placeholder="主叫号码"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.supplierName"
            placeholder="供应商名称"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.supplierNumber"
            placeholder="供应商编号"
            clearable
            @keyup.enter="search"
          />
        </div>
      </div>
      <div class="tw-flex tw-justify-between tw-items-center tw-mt-[12px]">
        <el-radio-group v-model="searchForm.recentMin" @change="search">
          <el-radio-button v-for="item in timeRangeList" :label="item.value" :key="item.value">{{ item.label }}</el-radio-button>
        </el-radio-group>
        <div class="tw-flex">
          <ColumnSetting
            :totalList="totalCols"
            :defaultList="defaultCols"
            :disabledList="disabledCols"
            :name="'supply-monitor'"
          />
          <el-button type="primary" @click="clearSearchForm" link>
            <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
            <span>重置</span>
          </el-button>
          <el-button type="primary" @click="search" link>
            <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
            <span>查询</span>
          </el-button>
        </div>
      </div>
    </div>
    <SupplyMonitorTable :table-data="tableData || []" :recentMin="searchForm.recentMin" showPagination :loading="!!loading" @update:table="search">
      <template v-slot:operate="{row}">
        <el-button type="primary" link @click="goEdit(row)">编辑</el-button>
        <el-button type="primary" link @click="goDetail(row)">详情</el-button>
      </template>
    </SupplyMonitorTable>
  </div>
</template>

<script lang="ts" setup>
import { reactive, computed, ref, onMounted, onDeactivated, onActivated } from 'vue'
import { ElMessage, } from 'element-plus'
import { lineSupplierModel } from '@/api/line'
import { SupplierParams, SupplierMonitorInfoItem, } from '@/type/line'
import router from '@/router'
import { goSupplyLinDetail, } from '@/utils/line'
import SupplyMonitorTable from '../components/SupplyMonitorTable.vue'
import to from 'await-to-js'
import { timeRangeList } from '../components/constant'
import { getColumnSettingByName} from '../components/constant'
import ColumnSetting from '@/components/ColumnSetting.vue'

const loading = ref(0)
// 列表及分页
const tableData = ref<SupplierMonitorInfoItem[] | null>([])
const {totalCols, defaultCols, disabledCols} = getColumnSettingByName('supply-monitor')

// 搜索内容
class searchOrigin {
  supplyLineName = undefined
  supplyLineNumber = undefined
  supplyLineType = undefined
  supplierName = undefined
  supplierNumber = undefined
  masterCallNumber = undefined
  recentMin = 5
}
const searchForm = reactive<SupplierParams>(new searchOrigin())

// 重置搜索内容
const clearSearchForm = () => {
  Object.assign(searchForm, new searchOrigin())
  ElMessage({
    type: 'success',
    message: '重置成功'
  })
}
const search = async () => {
  loading.value++
  const [err, res] = await to(lineSupplierModel.getMonitorList({
    ...searchForm,
    recentMin: searchForm.recentMin ? searchForm.recentMin : null
  })) as [any, SupplierMonitorInfoItem[]]
  tableData.value = (res||[]).sort((a, b) => b.currentlyCallNum - a.currentlyCallNum)
  loading.value--
}

const timer = ref()

// 列表进入监控详情
const goDetail = (row: SupplierMonitorInfoItem) => {
  router.push({
    name: 'SupplierLineMonitorDetails',
    query: {
      supplyLineNumber: row.supplyLineNumber,
      supplyLineName: row.supplyLineName,
      supplierNumber: row.supplierNumber,
    }
  })
}
// 编辑线路
const goEdit = async (row: SupplierMonitorInfoItem) => {
  goSupplyLinDetail(row)
}

onActivated(() => {
  search()
  timer.value = setInterval(() => {
    search()
  }, 5 * 60 * 1000)
})
const clearAllData = () => {
  clearInterval(timer.value)
  timer.value = null
  tableData.value = null
}
onDeactivated(() => {
  clearAllData()
})
</script>

<style scoped lang="postcss" type="text/postcss">
.line-manger-container {
  width: 100%;
  box-sizing: border-box;
  height: calc(100% - 48px);
  display: flex;
  flex-direction: column;
  .item {
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .label {
      width: 120px
    }
    :deep(.el-input__wrapper) {
      width: 100%;
    }
    .el-select {
      width: 100%;
    }
  }
  .el-table {
    font-size: 13px;
    :deep(.cell) {
      padding: 0 8px;
    }
  }
  .table-btn-box {
    display: flex;
    .el-button {
      width: 60px;
    }
  }
  :deep(.el-table .caret-wrapper) {
    display: none;
  }
}
</style>