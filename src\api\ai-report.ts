import { cancelRequest, http } from '@/axios'
import dayjs from 'dayjs'
import {
  OccupyRateEnum,
  RecordDialogueData,
  TaskCallSearchModal,
  TaskManageModel,
  TaskRestrictModal,
  TaskStatusEnum,
  TaskTypeEnum,
  TemplateBaseItem,
  TaskBatchItem,
  TaskPageModel,
  IntentionDataItem,
  LabelDataItem,
  ImportSearchModal,
  ImportRecordItem,
  ImportFailItem,
  TaskIntentionStatisticsItem,
  TaskFilterParamsInTool,
  AccountStatisticsSearchParams,
  AccountChartStatistics,
  AccountStatistics,
} from '@/type/task'
import { ResponseData } from '@/axios/request/types'
import { filterEmptyParams } from '@/utils/utils'
import { SeatCallParam, SeatTeam } from '@/type/seat'
import { SmsSearchModal, SmsRecord } from '@/type/sms'
import { useUserStore } from '@/store/user'
import { AdvancedRulesItem, } from '@/type/IntentionType'

export const aiOutboundTaskModel = {
  importPhones: (data: any, params: {id: number}) => {
    return http({
      data,
      params,
      url: "AiSpeech/aiOutboundTask/importPhonesFromExcel",
      method: "POST",
      timeout: 1000 * 60 * 10,
    }).then(res => res as unknown)
  },
  search: (params: TaskManageModel) => {
    const data = filterEmptyParams(params)
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/findList",
      method: "POST",
    }).then(res => res as unknown)
  },
  searchByAdditionCondition: (params: TaskManageModel) => {
    const data = filterEmptyParams(params)
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/findTaskAdditionalList",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 获取任务列表，返回仅包含任务名和id
  findTaskList: (params: TaskPageModel) => {
    const data = filterEmptyParams(params)
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/findListPageQuery",
      method: "POST",
    }).then(res => res as unknown as ResponseData)
  },
  findTaskListByGroupId: (params: TaskPageModel) => {
    const data = filterEmptyParams(params)
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/findListPageQueryForOperation",
      method: "POST",
    }).then(res => res as unknown as ResponseData)
  },
  findTaskNum: (params: {
    startTime: string,
    endTime: string,
    taskType: TaskTypeEnum,
  }) => {
    const data = filterEmptyParams(params)
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/findTaskNumByGroupId",
      method: "POST",
    }).then(res => res as unknown as number)
  },
  // 通过id或者ids字符串查询任务列表（时间为近3月）
  findTaskByIds: (idsStr: number | string) => {
    const data: TaskManageModel = {
      startTime: dayjs().add(-6, 'M').format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
      ids: idsStr + '',
    }
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/findList",
      method: "POST",
    }).then(res => res as unknown as TaskBatchItem[])
  },
  // 通过时间范围或者ids字符串，查询任务意向统计数据列表
  findTaskIntentionsStatistics: (data: any) => {
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/exportIntentions",
      method: "POST",
    }).then(res => res as unknown as TaskIntentionStatisticsItem[])
  },


  delete: (params: {
    id: number
  }) => {
    return http({
      params,
      url: "/AiSpeech/aiOutboundTask/deleteTask",
      method: "POST",
    }).then(res => res as unknown)
  },
  createTask: (data: TemplateBaseItem) => {
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/createTaskOnPage",
      method: "POST",
    }).then(res => res as unknown as TemplateBaseItem)
  },
  edit: (data: TemplateBaseItem) => {
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/edit",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 对执行中/已停止/未完成的任务，锁定操作
  lockTask: (data: {
    taskIds: string,
    taskType: TaskTypeEnum,
    groupId: string,
  }) => {
    return http({
      data: {
        taskIds: data.taskIds,
      },
      url: data.taskType === TaskTypeEnum['人机协同'] ? "/AiSpeech/aiOutboundManualTask/lockAiManualTask" : "/AiSpeech/aiOutboundTask/lockAiAutoTask",
      method: "POST",
    }).then(res => res as unknown)
  },
   // 对执行中/已停止/未完成的任务，解锁操作
  unlockTask: (data: {
    taskIds: string,
    taskType: TaskTypeEnum,
    groupId: string,
  }) => {
    return http({
      data: {
        taskIds: data.taskIds,
        groupId: data.groupId,
      },
      url: data.taskType === TaskTypeEnum['人机协同'] ? "/AiSpeech/aiOutboundManualTask/unLockAiManualTask" : "/AiSpeech/aiOutboundTask/unLockAiAutoTask",
      method: "POST",
    }).then(res => res as unknown)
  },
  getRates: (data: {
    taskId: number,
    groupId: string,
  }) => {
    return http({
      data,
      url: "/AiSpeech/phoneRecord/taskRates",
      method: "GET",
    }).then(res => res as unknown)
  },
  // 获取人机协同数据实际统计数据
  getManualStatistic: (params: {
    taskId: number, groupId: string
  }) => {
    return http({
      params,
      url: "/AiSpeech/aiOutboundManualTask/findStatistic",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 获取所有任务的实际并发
  getAllManualConcurrency: () => {
    return http({
      url: "/AiSpeech/aiOutboundManualTask/findAllRealConcurrency",
      method: "POST",
    }).then(res => res as unknown as Record<string, number>)
  },
  updateLineRatio: (params: {
    taskId: number, groupId: string, lineRatio: number
  }) => {
    return http({
      params,
      url: "/AiSpeech/aiOutboundManualTask/updateLineRatio",
      method: "POST",
    }).then(res => res as unknown)
  },
  updateOccupyRate: (params: {
    taskId: number, groupId: string, occupyRate: OccupyRateEnum
  }) => {
    return http({
      params,
      url: "/AiSpeech/aiOutboundManualTask/updateOccupyRate",
      method: "POST",
    }).then(res => res as unknown)
  },
  updateVirtualSeatRatio: (params: {
    taskId: number, groupId: string, virtualSeatRatio: number
  }) => {
    return http({
      params,
      url: "/AiSpeech/aiOutboundManualTask/updateVirtualSeatRatio",
      method: "POST",
    }).then(res => res as unknown)
  },
  setRestrictArea: (data: TaskRestrictModal) => {
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/setRestrictArea",
      method: "POST",
    }).then(res => res as unknown)
  },

  // 开启、结束任务
  startAiTask: (data: {
    taskId: number,
    callStatus: TaskStatusEnum,
    lineId?: number,
    lineName?: string,
    lineCode?: string,
    concurrent?: number,
  }) => {
    if(!data.lineId && data.callStatus === TaskStatusEnum['进行中']) return Promise.reject(new Error('请选择线路'))
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/startTask",
      method: "POST",
    }).then(res => res as unknown)
  },
  startMixTask: (data: {
    taskId: number,
    callStatus: TaskStatusEnum,
    lineId?: number,
    lineName?: string,
    lineCode?: string,
    concurrent?: number,
  }) => {
    if(!data.lineId && data.callStatus === TaskStatusEnum['进行中']) return Promise.reject(new Error('请选择线路'))
    return http({
      data,
      url: "/AiSpeech/aiOutboundManualTask/startAiManualTask",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 开启任务前预处理任务，检验任务数量级是否能对的上，-1：对的上； 具体数值：对不上
  preProcessTaskBeforeStart: (data: {
    taskId: number,
    callStatus: TaskStatusEnum,
    lineId?: number,
    lineName?: string,
    lineCode?: string,
    concurrent?: number,
  }) => {
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/preProcessSingleTask",
      method: "POST",
    }).then(res => res as unknown as number)
  },
  // 任务数量级对不上，需要在任务启动前重置任务量级
  resetTaskPhoneNum: (data: {
    taskId: number,
    callStatus: TaskStatusEnum,
    lineId?: number,
    lineName?: string,
    lineCode?: string,
    concurrent?: number,
  }) => {
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/resetTaskPhoneNum",
      method: "POST",
    }).then(res => res as unknown)
  },
  findCallQueueList: (data: TaskCallSearchModal) => {
    cancelRequest('/AiSpeech/callRecord/findFirstCallingPhoneList')
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/callRecord/findFirstCallingPhoneList",
      method: "POST",
    }).then(res => res as unknown)
  },
  findCallSupplyList: (data: TaskCallSearchModal) => {
    cancelRequest('/AiSpeech/callRecord/findReCallingPhoneList')
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/callRecord/findReCallingPhoneList",
      method: "POST",
    }).then(res => res as unknown)
  },
  findCallNameList: (data: TaskCallSearchModal) => {
    cancelRequest('/AiSpeech/callRecord/findFinishedPhoneList')
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/callRecord/findFinishedPhoneList",
      method: "POST",
    }).then(res => res as unknown)
  },
  importPhonesFromExcel: (data: FormData, params: { id: number }) => {
    return http({
      data,
      params,
      url: "/AiSpeech/aiOutboundTask/importPhonesFromExcel",
      method: "POST",
    }).then(res => res as unknown)
  },
  cancelCallQueue: (data: TaskCallSearchModal) => {
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/callRecord/cancelFirstCallingPhoneList",
      method: "POST",
    }).then(res => res as unknown)
  },
  cancelCallSupply: (data: TaskCallSearchModal) => {
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/callRecord/cancelReCallingPhoneList",
      method: "POST",
    }).then(res => res as unknown)
  },
  addAiFinishedPhoneList: (data: TaskCallSearchModal) => {
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/callRecord/addFinishedPhoneList",
      method: "POST",
    }).then(res => res as unknown)
  },
  addMixFinishedPhoneList: (data: TaskCallSearchModal) => {
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/callRecordAiManual/addAiManualFinishedPhoneList",
      method: "POST",
    }).then(res => res as unknown)
  },
  findCallRecordList: (data: TaskCallSearchModal, isOperation: boolean = false) => {
    data.startPage = (data.startPage! >= 0) ? data.startPage : 0
    isOperation ? cancelRequest('/AiSpeech/callRecord/callRecordListForOperation') : cancelRequest('/AiSpeech/callRecord/callRecordList')
    return http({
      data: filterEmptyParams(data),
      url: isOperation ? "/AiSpeech/callRecord/callRecordListForOperation" : "/AiSpeech/callRecord/callRecordList",
      method: "POST",
    }).then(res => res as unknown as ResponseData)
  },
  findCallRecordMixList: (data: TaskCallSearchModal, isOperation: boolean = false) => {
    data.startPage = (data.startPage! >= 0) ? data.startPage : 0
    isOperation ? cancelRequest('/AiSpeech/callRecordAiManual/callRecordAiManualListForOperation') : cancelRequest('/AiSpeech/callRecordAiManual/callRecordAiManualList')
    return http({
      data: filterEmptyParams(data),
      url: isOperation ? '/AiSpeech/callRecordAiManual/callRecordAiManualListForOperation' : '/AiSpeech/callRecordAiManual/callRecordAiManualList',
      method: "POST",
    }).then(res => res as unknown as ResponseData)
  },
  findCallRecordManualList: (data: TaskCallSearchModal, isOperation: boolean = false) => {
    data.startPage = (data.startPage! >= 0) ? data.startPage : 0
    isOperation ? cancelRequest('/AiSpeech/callRecordManual/callRecordManualListForOperation') : cancelRequest('/AiSpeech/callRecordManual/callRecordManualList')
    return http({
      data: filterEmptyParams(data),
      url: isOperation ? "/AiSpeech/callRecordManual/callRecordManualListForOperation" : "/AiSpeech/callRecordManual/callRecordManualList",
      method: "POST",
    }).then(res => res as unknown as ResponseData)
  },
  // 查看最近的ai通话记录
  findFollowCallRecordList: (data: TaskCallSearchModal) => {
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/callRecord/findFollowCallRecordList",
      method: "POST",
    }).then(res => res as unknown as ResponseData)
  },
  // 查看人机协同的明文号码
  convertPlainPhoneByRecordId: (data: {recordId: string}) => {
    return http({
      data,
      url: "/AiSpeech/callRecordAiManual/convertPlainPhoneByRecordId",
      method: "GET",
    }).then(res => res as unknown)
  },
  findSmsRecordList: (data: SmsSearchModal, isOperation: boolean = false) => {
    data.startPage = (data.startPage! >= 0) ? data.startPage : 0
    isOperation ? cancelRequest('/AiSpeech/message/findMessageRecordListForOperation') : cancelRequest('/AiSpeech/message/findMessageRecordList')
    return http({
      data: filterEmptyParams(data),
      url: isOperation ? "/AiSpeech/message/findMessageRecordListForOperation" : "/AiSpeech/message/findMessageRecordList",
      method: "POST",
    }).then(res => res as unknown as ResponseData)
  },
  findSmsRecordByRecordId: (data: {callOutTime: string, callRecordId: string}) => {
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/message/findSmsTypeListByRecordId",
      method: "POST",
    }).then(res => res as unknown as SmsRecord[])
  },
  // 通过任务id列表，获取任务->项目，产品，行业的对应关系
  findProgramByTaskIds: (data: number[]) => {
    cancelRequest('/AiSpeech/aiOutboundTask/findProductIdProgramIdIndustryId')
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/findProductIdProgramIdIndustryId",
      method: "POST",
    }).then(res => res as unknown as {id: number, programId: string}[])
  },
  batchPreProcess: async (data: {
    taskIds: string
  }) => {
    return http({
      url: '/AiSpeech/aiOutboundTask/batchPreProcess',
      method: 'POST',
      data,
    }).then(res => res as unknown)
  },
  batchEdit: (data: TaskManageModel) => {
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/editBatch",
      method: "POST",
    }).then(res => res as unknown as {id: number, batchStatus: string}[])
  },
  // 批量编辑话术，仅改话术，不改联动信息
  batchEditScript: (data: TaskManageModel) => {
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/volcanoEditScriptBatch",
      method: "POST",
    }).then(res => res as unknown as {id: number, batchStatus: string}[])
  },
  // 外呼工具，导入任务，返回任务ids
  batchImportTaskIdsInTool: (data: TaskFilterParamsInTool) => {
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/aiOutboundTask/findTaskListByAITool",
      method: "POST",
    }).then(res => res as unknown as number[])
  },
  // 批量设置屏蔽地区（覆盖）
  batchSetRestrictArea: (data: TaskRestrictModal) => {
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/setRestrictAreaBatch",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 批量设置屏蔽地区（增量）
  batchSetRestrictAreaAppend: (data: TaskRestrictModal) => {
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/setRestrictAreaAppendBatch",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 批量设置屏蔽地区（递减）
  batchSetRestrictAreaReduce: (data: TaskRestrictModal) => {
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/setRestrictAreaReduceBatch",
      method: "POST",
    }).then(res => res as unknown)
  },
  batchStartAiTask: (data: {
    taskIds: string,
    callStatus: TaskStatusEnum,
    lineId?: number,
    lineName?: string,
    lineCode?: string,
    lineRemainConcurrent?: number,
    expectedFinishTime?: string,
    includeAutoStop?: string,
    concurrent?: number,
  }) => {
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/startTaskBatch",
      method: "POST",
    }).then(res => res as unknown)
  },
  getNeededConcurrent: (data: {
    taskIds: string,
    expectedStartTime?: string,
    expectedFinishTime?: string,
  }) => {
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/neededConcurrentTotal",
      method: "POST",
    }).then(res => res as unknown as number)
  },
  batchStartMixTask: (data: {
    taskIds: string,
    callStatus: TaskStatusEnum,
    lineId?: number,
    lineName?: string,
    lineCode?: string,
    lineRemainConcurrent?: number,
    expectedFinishTime?: string,
    includeAutoStop?: string,
  }) => {
    return http({
      data,
      url: "/AiSpeech/aiOutboundManualTask/startAiManualTaskBatch",
      method: "POST",
    }).then(res => res as unknown)
  },
  batchStopAiTask: (data: {
    taskIds: string,
    callStatus: TaskStatusEnum,
  }) => {
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/stopTaskBatch",
      method: "POST",
    }).then(res => res as unknown)
  },
  batchStopMixTask: (data: {
    taskIds: string,
    callStatus: TaskStatusEnum,
  }) => {
    return http({
      data,
      url: "/AiSpeech/aiOutboundManualTask/stopAiManualTaskBatch",
      method: "POST",
    }).then(res => res as unknown)
  },
  batchFilterAiFinishedPhoneNum: (data: TaskCallSearchModal) => {
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/callRecord/findFinishedPhoneNum",
      method: "POST",
    }).then(res => res as unknown)
  },
  batchFilterMixFinishedPhoneNum: (data: TaskCallSearchModal) => {
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/callRecordAiManual/findAiManualFinishedPhoneNum",
      method: "POST",
    }).then(res => res as unknown)
  },
  getCallRecordAiNum: (data: TaskCallSearchModal) => {
    cancelRequest('/AiSpeech/callRecord/callRecordNum')
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/callRecord/callRecordNum",
      method: "POST",
    }).then(res => res as unknown)
  },
  getCallRecordMixNum: (data: TaskCallSearchModal) => {
    cancelRequest('/AiSpeech/callRecordAiManual/callRecordAiManualNum')
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/callRecordAiManual/callRecordAiManualNum",
      method: "POST",
    }).then(res => res as unknown)
  },
  getCallRecordManualNum: (data: TaskCallSearchModal) => {
    cancelRequest('/AiSpeech/callRecordManual/callRecordManualNum')
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/callRecordManual/callRecordManualNum",
      method: "POST",
    }).then(res => res as unknown)
  },
  getSmsRecordNum: (data: SmsSearchModal) => {
    cancelRequest('/AiSpeech/message/findMessageRecordListNum')
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/message/findMessageRecordListNum",
      method: "POST",
    }).then(res => res as unknown)
  },
  batchAddAiFinishedPhoneList: (data: TaskCallSearchModal) => {
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/callRecord/addFinishedPhoneListBatch",
      method: "POST",
      timeout: 1000 * 60 * 10,
    }).then(res => res as unknown)
  },
  batchAddMixFinishedPhoneList: (data: TaskCallSearchModal) => {
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/callRecordAiManual/addAiManualFinishedPhoneListBatch",
      method: "POST",
      timeout: 1000 * 60 * 10,
    }).then(res => res as unknown)
  },
  batchAddAiCallRecordList: (data: TaskCallSearchModal,) => {
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/callRecord/addCallRecordListBatch",
      method: "POST",
      timeout: 1000 * 60 * 10,
    }).then(res => res as unknown)
  },
  batchAddMixCallRecordList: (data: TaskCallSearchModal,) => {
    return http({
      data: filterEmptyParams(data),
      url: "/AiSpeech/callRecordAiManual/addAiManualCallRecordListBatch",
      method: "POST",
      timeout: 1000 * 60 * 10,
    }).then(res => res as unknown)
  },
  // 取消第一次呼叫
  batchCancelFirstCallPhoneList: (data: number[]) => {
    return http({
      data,
      url: "/AiSpeech/callRecord/cancelFirstCallLinePhoneListBatch",
      method: "POST",
      timeout: 1000 * 60 * 10,
    }).then(res => res as unknown)
  },
  // 取消第一次呼叫
  batchCancelReCallPhoneList: (data: number[]) => {
    return http({
      data,
      url: "/AiSpeech/callRecord/cancelAllReCallingPhoneListBatch",
      method: "POST",
      timeout: 1000 * 60 * 10,
    }).then(res => res as unknown)
  },
  getAiDialogueDataList: (data: {
    callId: string,
    recordId: string,
  }) => {
    cancelRequest('/AiSpeech/dialog/getAiDialogueDataList')
    return http({
      data,
      url: "/AiSpeech/dialog/getAiDialogueDataList",
      method: "GET",
    }).then(res => res as unknown as RecordDialogueData[])
  },
  getMixDialogueDataList: (data: {
    callId: string,
    recordId: string,
  }) => {
    cancelRequest('/AiSpeech/dialog/getAiManualDialogueDataList')
    return http({
      data,
      url: "/AiSpeech/dialog/getAiManualDialogueDataList",
      method: "GET",
    }).then(res => res as unknown as RecordDialogueData[])
  },
  getManualDialogueDataList: (data: {
    callId: string,
    recordId: string,
  }) => {
    cancelRequest('/AiSpeech/dialog/getManualDialogueDataList')
    return http({
      data,
      url: "/AiSpeech/dialog/getManualDialogueDataList",
      method: "GET",
    }).then(res => res as unknown as RecordDialogueData[])
  },

  // 查看通话记录高级规则详情(会包括历史高级规则)
  findAdvancedRulesInRecord: (data: {
    ruleIds: string,
  }) => {
    return http({
      data,
      url: "/AiSpeech/advanced-rules/findAdvancedRulesInRecord",
      method: "GET",
    }).then(res => res as unknown as AdvancedRulesItem[])
  },

  // 转为线索
  // ai外呼转为线索
  aiRecordConvert2Clues: (params: {
    groupId: string,
    isToday: boolean,
    recordId: string
  }) => {
    return http({
      params,
      url: "/AiSpeech/clue/aiCallRecordIntoClues",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 人机协同外呼转为线索
  mixRecordConvert2Clues: (params: {
    groupId: string,
    isToday: boolean,
    recordId: string
  }) => {
    return http({
      params,
      url: "/AiSpeech/clue/aiManualCallRecordIntoClues",
      method: "POST",
    }).then(res => res as unknown)
  },
  // 根据任务的ID列表获取任务列表
  getTaskListByTaskIds: (data: number[]) => {
    return http({
      data,
      url: "/AiSpeech/aiOutboundTask/findTasksByTaskIds",
      method: "POST",
    }).then(res => res as unknown)
  },
}

export const aiOutboundTaskTemplateModel = {
  search: (data: {
    name?: string,
    taskType?: TaskTypeEnum,
    groupId?: string
  }) => {
    const { accountType } = useUserStore()
    return http({
      data: filterEmptyParams(data),
      url: accountType == 0 ? "/AiSpeech/aiOutboundTaskTemplate/operatorFindByName" : "/AiSpeech/aiOutboundTaskTemplate/findByName",
      method: "POST",
    }).then(res => res as unknown as TemplateBaseItem[])
  },
  
  delete: (data: {
    id: number
  }) => {
    return http({
      data,
      url: `/AiSpeech/aiOutboundTaskTemplate/deleteById`,
      method: "GET",
    }).then(res => res as unknown)
  },
  switchStatus: (data: {
    id: number,
    templateStatus?: number
  }) => {
    return http({
      data,
      url: `/AiSpeech/aiOutboundTaskTemplate/updateTemplateStatus`,
      method: "POST",
    }).then(res => res as unknown)
  },
  edit: (data: TemplateBaseItem) => {
    return http({
      data,
      url: "/AiSpeech/aiOutboundTaskTemplate/save",
      method: "POST",
    }).then(res => res as unknown)
  },
  findAllCallTeams: (data: {
    groupId: string
  }) => {
    return http({
      data,
      url: "/AiSpeech/callSeatManager/findAllCallTeamsByGroupId",
      method: "GET",
    }).then(res => res as unknown as SeatTeam[])
  },
  // 通过任务模板id定位商户主账号
  findByTemplateId: (params: {
    id: string
  }) => {
    return http({
      params,
      url: "/AiSpeech/aiOutboundTaskTemplate/findTemplatePositionById",
      method: "POST",
    }).then(res => res as unknown as (TemplateBaseItem & {account: string}) | null)
  },

}

// AI外呼任务数据
export const aiOutboundTaskChartModel = {
  // 地区
  getRegion: (data: {
    taskId: number | string | null
    operators: string,
    groupId: string,
    queryDate?: string
  }) => {
    return http({
      url: `/AiSpeech/aiOutboundTask/distribution`,
      method: 'POST',
      data
    }).then(res => res as unknown)
  },
}

// 账号统计报表
export const statisticsModel = {
  // 获取统计文本数据
  getTaskStatistics: (data: {
    queryDate: string
  }) => {
    return http({
      url: '/AiSpeech/aiOutboundTask/taskStatistic',
      method: 'GET',
      data
    }).then(res => res as unknown as AccountStatistics)
  },
  // 运营端：获取统计文本数据
  getTaskStatisticsByGroupId: (data: {
    queryDate: string,
    groupId: string,
  }) => {
    return http({
      url: '/AiSpeech/aiOutboundTask/taskStatisticForOperation',
      method: 'GET',
      data
    }).then(res => res as unknown as AccountStatistics)
  },
  /**----------------------------数据概览-------------------------------------- */
  // 漏斗图统计
  getFunnel: (data: AccountStatisticsSearchParams) => {
    return http({
      url: `/AiMonitor/statistics/funnelChart`,
      method: 'POST',
      data: filterEmptyParams(data)
    }).then(res => res as unknown)
  },
  // 单个任务分类统计
  getCategory: (data: AccountStatisticsSearchParams) => {
    return http({
      url: `/AiMonitor/statistics/classRatio`,
      method: 'POST',
      data: filterEmptyParams(data)
    }).then(res => res as unknown)
  },
  // 意向分类统计
  getIntentionList: (data: AccountStatisticsSearchParams) => {
    return http({
      url: `/AiMonitor/statistics/getScriptIntentionClassStatisticList`,
      method: 'POST',
      data: filterEmptyParams(data)
    }).then(res => res as unknown as Record<string, number>)
  },
  // 意向标签统计
  getLabelList: (data: AccountStatisticsSearchParams) => {
    return http({
      url: `/AiMonitor/statistics/getScriptLabelStatisticList`,
      method: 'POST',
      data: filterEmptyParams(data)
    }).then(res => res as unknown as { putThroughNum: number, labelList: LabelDataItem[]})
  },
  /**--------------------------------数据统计---------------------------------- */
  // 获取数据统计-筛选条件下的统计数据
  findStatisticsByCondition: (data: AccountStatisticsSearchParams) => {
    return http({
      url: '/AiMonitor/statistics/getDataRateChart',
      method: 'POST',
      data: filterEmptyParams(data)
    }).then(res => res as unknown as AccountChartStatistics)
  },
  // 获取数据统计-筛选条件下的时间分布
  findTimeDistributionByCondition: (data: AccountStatisticsSearchParams) => {
    return http({
      url: '/AiMonitor/statistics/getRateChart',
      method: 'POST',
      data: filterEmptyParams(data)
    }).then(res => res as unknown as Record<string, {num: number, total:number,}>)
  },
  // 获取数据统计-筛选条件下的全国分布
  findCountryDistributionByCondition: (data: AccountStatisticsSearchParams) => {
    return http({
      url: '/AiMonitor/statistics/getCountryChart',
      method: 'POST',
      data: filterEmptyParams(data)
    }).then(res => res as unknown as Record<string, {
      num: number,
      total: number,
    }>)
  },
  // 获取数据统计-筛选条件下的某省份分布
  findProvinceDistributionByCondition: (data: AccountStatisticsSearchParams) => {
    return http({
      url: '/AiMonitor/statistics/getProvinceChart',
      method: 'POST',
      data: filterEmptyParams(data)
    }).then(res => res as unknown as Record<string, {
      num: number,
      total: number,
    }>)
  },
}
const apiFox = "http://127.0.0.1:4523/m1/4319339-3962297-default"
// 导入记录
export const importModel = {
  // 获取统计文本数据
  findImportList: (data: ImportSearchModal) => {
    return http({
      url: '/AiSpeech/importAiOutboundTaskRecord/findImportPhoneRecordList',
      method: 'POST',
      data: filterEmptyParams(data),
    }).then(res => res as unknown as ImportRecordItem[])
  },
  findFailById: (data: {batchId: number}) => {
    return http({
      url: '/AiSpeech/importAiOutboundTaskRecord/findImportFailPhoneRecordByBatchId',
      method: 'GET',
      data,
    }).then(res => res as unknown as ImportFailItem[])
  }
}
