<template>
  <el-dialog
    v-model="dialogVisible"
    width="600px"
    align-center
    :close-on-click-modal="false"
    @close="closeDialog"
    class="dialog-form"
  >
    <template #header>
      <div class="tw-text-left tw-text-[16px] tw-font-semibold">
        {{ props.content.id! > -1 ? '编辑账号' : '创建账号' }}
      </div>
    </template>
    <el-scrollbar
      :max-height="'calc(100vh - 200px)'"
      wrap-class="tw-p-[12px]"
      ref="scrollbarRef"
    >
      <el-form
        ref="form"
        :model="content"
        :rules="rules"
        label-width="90px"
      >
        <div class="form-section">
          <div class="form-section-header">
            账号信息
          </div>
          <!--编辑账号时，不允许修改账号名和密码-->
          <el-form-item label="登录账号：" prop="account">
            <el-input v-model.trim="content.account" placeholder="填写登录账号" clearable :disabled="props.content?.id!>-1" autocomplete="new-password" />
          </el-form-item>
          <el-form-item label="密码：" prop="password">
            <el-input v-model.trim="content.password" placeholder="填写密码" clearable show-password :disabled="props.content?.id!>-1" autocomplete="new-password" />
          </el-form-item>
          <el-form-item label="确认密码：" prop="passwordConfirmed">
            <el-input v-model.trim="content.passwordConfirmed" placeholder="确认密码" clearable show-password :disabled="props.content?.id!>-1" autocomplete="new-password" />
          </el-form-item>
        </div>

        <div class="form-section">
          <div class="form-section-header">
            联系人信息
          </div>
          <el-form-item label="联系人：" prop="name">
            <el-input v-model.trim="content.name" placeholder="填写联系人" clearable />
          </el-form-item>
          <el-form-item label="联系电话：" prop="phone">
            <el-input v-model.trim="content.phone" placeholder="填写联系电话" clearable />
          </el-form-item>
          <el-form-item label="邮箱：" prop="email">
            <el-input v-model.trim="content.email" placeholder="填写邮箱地址" clearable />
          </el-form-item>
          <el-form-item label="联系地址：" prop="address">
            <el-input
              v-model.trim="content.address"
              type="textarea"
              placeholder="填写联系地址，不超过250字"
              clearable
              maxlength="250"
              show-word-limit
              autosize
              resize="none"
            />
          </el-form-item>
        </div>

        <div class="form-section">
          <div class="form-section-header">
            启用状态
          </div>
          <el-form-item label="启用状态：" prop="enableStatus">
            <el-select v-model.trim="content.accountEnable" placeholder="选择启用状态">
              <el-option
                v-for="merchantAccountStatusItem in merchantAccountStatusList"
                :key="merchantAccountStatusItem.name"
                :value="merchantAccountStatusItem.value"
                :label="merchantAccountStatusItem.name"
              />
            </el-select>
          </el-form-item>
        </div>

        <div class="form-section">
          <div class="form-section-header">
            数据传输
          </div>
          <el-form-item label="号码类型：" prop="isForEncryptionPhones">
            <el-radio-group v-model="content.isForEncryptionPhones" :disabled="props.content?.id! > -1">
              <el-radio :label="false">普通</el-radio>
              <el-radio :label="true">加密</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item v-if="!content.isForEncryptionPhones" prop="isForEncryptionPhones">
            <template #label>
              <div class="tw-flex tw-items-center">
                <span>明文类型</span>
                <el-tooltip content="导入时加密，落库时解密（解密方式和业务方对接），回调时再按原加密形式加密" placement="top">
                  <template #content>
                    <div class="tw-flex tw-flex-col tw-text-[13px] tw-max-w-[35vw] tw-text-justify">
                      <div class="tw-mb-[4px] tw-pb-[4px] tw-border-b-[1px]">明文类型</div>
                      <div>全程明文：导入和回调，全部明文传输</div>
                      <div>解密明文：导入时加密，落库时解密（解密方式和业务方对接），回调时再按原加密形式加密</div>
                    </div>
                  </template>
                  <el-icon :size="'var(--el-font-size-base)'" color="var(--primary-black-color-400)" class="tw-ml-[2px]"><SvgIcon name="warning"/></el-icon>
                </el-tooltip>
                <span>：</span>
              </div>
            </template>
            <el-radio-group v-model="content.isForEncryptionAgain" :disabled="props.content?.id! > -1">
              <el-radio :label="false">全程明文</el-radio>
              <el-radio :label="true">解密明文</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>

        <div class="form-section">
          <div class="form-section-header">
            运营备注
          </div>
          <el-form-item label="备注：">
            <el-input
              v-model.trim="content.note"
              type="textarea"
              placeholder="填写备注，不超过250字（选填）"
              clearable
              maxlength="250"
              show-word-limit
              autosize
              resize="none"
            />
          </el-form-item>
        </div>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog" :icon="CloseBold">取消</el-button>
        <el-button :loading="loading" type="primary" @click="handleSubmit" :icon="Select">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import { pickAttrFromObj } from '@/utils/utils'
import { CloseBold, Select } from '@element-plus/icons-vue'
import { MerchantAccountInfo } from '@/type/merchant'
import { merchantUserModel } from '@/api/user'
import { merchantAccountStatusList, AccountOriginalData } from './constant'
import to from 'await-to-js'
import { trace } from '@/utils/trace'

const props = defineProps<{
  visible: boolean
  content: MerchantAccountInfo
  merchantId: number | null
  accountList: MerchantAccountInfo[]
}>()

const emits = defineEmits(['update:visible', 'update'])

const dialogVisible = ref(false)
const loading = ref(false)

const content = reactive<MerchantAccountInfo>(new AccountOriginalData())
const rules = {
  account: [{
    required: true, trigger: 'blur', validator: (rule: any, value: any, callback: any) => {
      // 创建账号时进行校验，编辑账号时不校验，因为编辑账号时不允许修改账号名
      if (props.content?.id! === -1) {
        // 登录账号不能为空
        if (value === '') {
          callback(new Error('登录账号不能为空'))
          return
        }

        // 同一个商户里账号不能重名
        const hasSameAccount = props.accountList.some((item) => {
          return item?.account === value
        })
        if (hasSameAccount) {
          callback(new Error('已存在此账号，请勿重复添加'))
          return
        }

        // 账号名只能字母数字，限定长度
        if (!(/^[0-9A-Za-z]{1,30}$/).test(value)) {
          callback(new Error('请输入字母或数字，30字符以内'))
          return
        }
      }

      callback()
    },
  }],
  password: [{
    required: true, trigger: 'blur', validator: (rule: any, value: any, callback: any) => {
      if (props.content?.id! > -1) {
        // 编辑时，不允许编辑密码，不做校验
        callback()
      } else {
        // 创建时
        if (value === '') {
          callback(new Error('密码不能为空'))
        } else {
          callback()
        }
      }
    },
  }],
  passwordConfirmed: [{
    required: true, trigger: 'blur', validator: (rule: any, value: any, callback: any) => {
      if (props.content?.id! > -1) {
        // 编辑时，不允许编辑密码，不做校验
        callback()
      } else {
        // 创建时
        if (value.trim() === '') {
          callback(new Error('确认密码不能为空'))
        } else if (content.password !== '' && value !== content.password) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      }
    },
  }],
  isForEncryptionPhones: [{ required: true, trigger: 'change', message: '数据传输方式不能为空' }],
  name: [{ required: true, trigger: 'blur', message: '联系人不能为空' }],
  phone: [{
    trigger: 'blur', validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback()
      } else {
        if (/^\d{5,20}$/.test(value)) {
          callback()
        } else {
          callback(new Error('联系电话格式不正确，请输入5-20位数字'))
        }
      }
    }
  }],
  email: [{
    trigger: 'blur', validator: (rule: any, value: any, callback: any) => {
      if (!value) {
        callback()
      } else {
        // a@b.c
        // 这5个基本元素都不能少
        // a b c 可以是大小英文字母、中文、数字、下划线、连字符
        // .c 可以重复匹配
        if (/^[A-Za-z0-9\u4e00-\u9fa5_-]+@[a-zA-Z0-9\u4e00-\u9fa5_-]+(\.[a-zA-Z0-9\u4e00-\u9fa5_-]+)+$/.test(value)) {
          callback()
        } else {
          callback(new Error('邮箱格式不正确，类似*******************的格式'))
        }
      }
    }
  }],
  address: [{ required: false, trigger: 'blur', message: '联系地址不能为空' }],
  accountEnable: [{ required: true, trigger: 'blur', message: '启用状态不能为空' }],
}

const form = ref()

const closeDialog = () => {
  dialogVisible.value = false
  emits('update:visible', false)
}

const handleSubmit = async () => {
  const [err] = await to(form.value.validate())
  if (err) return

  loading.value = true
  const params: Partial<MerchantAccountInfo> = pickAttrFromObj(content, [
    'id', 'account', 'password', 'name', 'phone', 'email', 'address', 'accountEnable', 'note', 
    'roleId', 'groupId', 'isForEncryptionPhones', 'isForEncryptionAgain',
  ])
  if (params.isForEncryptionPhones) {
    params.isForEncryptionAgain = undefined
  }
  params.tenantId = props.merchantId ?? -1
  params.accountType = 1
  params.gender = 'MALE'
  params.department = ''

  if (params.id === -1) {
    params.id = undefined
    params.roleId = undefined
    params.groupId = undefined
  } else {
    params.password = undefined
  }
  trace({
    params,
    page: `商户管理-${props.content?.id! > -1 ? '编辑' : '创建'}账号`,
  })
  const [err1, res1] = await to(params.id ? merchantUserModel.editAccount(params) : merchantUserModel.addAccount(params))
  loading.value = false
  if (err1) {
    return
  }
  emits('update', res1)
  closeDialog()
}

const scrollbarRef = ref()
watch(() => props.visible, (val) => {
  dialogVisible.value = val
  if (val) {
    Object.assign(content, props.content?.id! > -1 ? props.content : new AccountOriginalData())
    if (props.content?.id! > -1) {
      content.password = 'fake_password'
      content.passwordConfirmed = 'fake_password'
    } else {
      content.password = ''
      content.passwordConfirmed = ''
    }
    content.isForEncryptionAgain = content.isForEncryptionAgain ?? false
    setTimeout(() => {
      scrollbarRef.value?.setScrollTop(0)
      form.value?.clearValidate()
    }, 100)
  }
})
</script>

<style lang="postcss" scoped>
.form-section {
  .form-section-header {
    text-align: left;
    margin: 6px 0;
  }
}
</style>
