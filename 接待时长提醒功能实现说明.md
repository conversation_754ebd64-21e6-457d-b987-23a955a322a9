# 接待时长提醒功能实现说明

## 功能概述

在坐席接待时长超过25分钟后，系统会自动显示ElMessage提示，提醒坐席尽快结束当前通话。

## 实现细节

### 1. 核心功能
- **触发条件**: 接待时长达到25分钟（1500秒）
- **提示内容**: "根据业务要求，接待时长不宜超过30分钟，请尽快结束当前通话！"
- **提示类型**: 警告类型（warning）
- **显示特性**: 只显示关闭按钮，不自动关闭

### 2. 交互方式
- ✅ **点击关闭按钮**: 可以关闭提示
- ✅ **按ESC键**: 可以关闭提示  
- ✅ **点击遮罩层**: ElMessage本身支持点击遮罩层关闭

### 3. 代码修改

#### 3.1 Store文件修改 (`src/store/seat-phone.ts`)

**新增变量:**
```typescript
// 是否已显示25分钟提示
let has25MinuteWarningShown = false
// 25分钟警告消息实例
let receptionWarningMessage: any = null
```

**修改 `resetReceptionTimer` 函数:**
```typescript
const resetReceptionTimer = () => {
  receptionSecond.value = 0
  has25MinuteWarningShown = false
  // 关闭之前的警告消息
  if (receptionWarningMessage) {
    receptionWarningMessage.close()
    receptionWarningMessage = null
  }
}
```

**修改 `handleReceptionTimer` 函数:**
```typescript
const handleReceptionTimer = () => {
  receptionSecond.value = dayjs().diff(receptionStartDatetime, 'second') ?? 0
  
  // 检查是否超过25分钟（1500秒）且未显示过提示
  if (receptionSecond.value >= 1500 && !has25MinuteWarningShown) {
    has25MinuteWarningShown = true
    
    // 创建警告消息
    receptionWarningMessage = ElMessage({
      message: '根据业务要求，接待时长不宜超过30分钟，请尽快结束当前通话！',
      type: 'warning',
      showClose: true,
      duration: 0, // 不自动关闭
      customClass: 'reception-time-warning',
      onClose: () => {
        receptionWarningMessage = null
      }
    })
    
    // 添加ESC键监听器
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && receptionWarningMessage) {
        receptionWarningMessage.close()
        document.removeEventListener('keydown', handleEscKey)
      }
    }
    document.addEventListener('keydown', handleEscKey)
  }
}
```

**修改 `closeReceptionTimer` 函数:**
```typescript
const closeReceptionTimer = () => {
  if (typeof receptionTimer === 'number') {
    clearInterval(receptionTimer)
  }
  receptionTimer = null
  
  // 关闭警告消息
  if (receptionWarningMessage) {
    receptionWarningMessage.close()
    receptionWarningMessage = null
  }
}
```

#### 3.2 样式文件修改 (`src/index.pcss`)

**新增样式:**
```css
/* 接待时长警告消息 */
.reception-time-warning {
  &.el-message {
    --el-message-close-size: 16px;
    --el-message-close-hover-color: #909399;
    z-index: 3000;
    min-width: 380px;
    
    .el-message__content {
      font-size: 14px;
      font-weight: 500;
      line-height: 1.4;
    }
    
    .el-message__closeBtn {
      font-size: 16px;
      cursor: pointer;
      
      &:hover {
        color: var(--el-message-close-hover-color);
      }
    }
  }
}
```

### 4. 功能特点

#### 4.1 防重复显示
- 使用 `has25MinuteWarningShown` 标志确保每次通话只显示一次提示
- 在重置定时器时重置标志

#### 4.2 消息管理
- 保存消息实例引用，便于主动关闭
- 在定时器关闭时自动清理消息

#### 4.3 键盘交互
- 监听ESC键事件
- 在消息关闭时移除事件监听器，避免内存泄漏

#### 4.4 样式定制
- 自定义CSS类名 `reception-time-warning`
- 设置合适的z-index确保显示在最前面
- 优化字体大小和间距

### 5. 测试验证

创建了测试页面 `test-reception-timer.html` 用于验证功能：
- 模拟接待计时器
- 测试25分钟提醒功能
- 验证各种关闭方式（关闭按钮、ESC键）
- 提供快进功能便于测试

### 6. 使用场景

该功能会在以下情况下触发：
1. **人工直呼**: 接听电话后开始计时
2. **人机协同介入**: 介入通话后开始计时
3. **人机协同接管**: 接管通话后开始计时

### 7. 注意事项

1. **时间计算**: 使用dayjs计算时间差，精确到秒
2. **内存管理**: 及时清理事件监听器和消息实例
3. **用户体验**: 提示不会自动消失，需要用户主动关闭
4. **兼容性**: 使用标准的键盘事件API，兼容性良好

## 总结

该功能完全满足需求：
- ✅ 在25分钟后显示提示
- ✅ 使用ElMessage组件
- ✅ 只显示关闭按钮
- ✅ 支持点击关闭按钮退出
- ✅ 支持按ESC键退出
- ✅ 支持点击遮罩层关闭（ElMessage原生支持）
- ✅ 提示内容准确："根据业务要求，接待时长不宜超过30分钟，请尽快结束当前通话！"
