import { SupplierBlackGroupInfo, SupplierBlackGroupParam, SupplierLineInfo } from '@/type/supplier'
import { FormRules } from 'element-plus'
/**
 * 黑名单分组，生效范围，格式化文本
 * @param {SupplierBlackGroupInfo} row 靓号限制信息
 */
export const formatBlackGroupScopeText = (row: SupplierBlackGroupInfo) => {
  // 全部
  if (row?.isAllLinesActive) {
    return '全部'
  }

  // 部分
  let arr: string[] = []
  row.supplyLines?.forEach(line => {
    line?.lineName && arr.push(line?.lineName)
  })
  return arr.join('、') || '-'
}

export const rules: FormRules = {
  supplyLineNumbers: { required: true, message: '生效范围不能为空', trigger: ['blur', 'change'],},
  blackListGroupId: { required: true, trigger: ['blur', 'change'], message: '挂载黑名单不能为空',},
}

export class SupplierBlackGroupOrigin implements SupplierBlackGroupParam {
  constructor(id: number) {
    this.callLineSupplierId = id
  }
  
  callLineSupplierId: number
  blackListGroupId = undefined
  allLinesActive = true
  supplyLineNumbers: string[] = []
}
