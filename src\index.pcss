@tailwind base;
@tailwind components;
@tailwind utilities;
html, body, #app {
  overflow: hidden;
  height: 100%;
  width: 100%;
  color: #2c3e50;
  text-align: center;
}
#app {
  --el-font-size-base: 13px;
  --el-color-primary: #165DFF;
  --el-color-danger: #E54B17;
  --color-info: #E6E6E6;
  --el-text-color-primary: #626366;
  --primary-green-color: #13BF77;
  --primary-blue-color: #165DFF;
  --primary-red-color: #E54B17;
  --primary-orange-color: #E59000;
  --primary-yellow-color: #FFCC00;
  --primary-gray-color: #969799;
  --primary-black-color-50: #f0f2f5;
  --primary-black-color-100: #EBEDF0;
  --primary-black-color-200: #E1E3E6;
  --primary-black-color-300: #C8C9CC;
  --primary-black-color-400: #969799;
  --primary-black-color-500: #626366;
  --primary-black-color-600: #313233;
  --el-input-text-color: var(--primary-black-color-600);
  --el-text-color-regular: var(--primary-black-color-600);
  --el-table-header-text-color: #626366;
  --el-text-color-secondary: #626366;
  @media screen and (max-width: 600px) {
    --el-font-size-base: 10px;
  }
}
.el-form .el-checkbox {
  .el-checkbox__label {
    --el-checkbox-font-size: 13px;
  }
  .el-checkbox__input {
    --el-checkbox-input-width: 13px;
    --el-checkbox-input-height: 13px;
  }
}
.el-divider.el-divider--horizontal {
  margin: 12px 0;
  .el-divider__text {
    font-size: 13px;
    height: 16px;
    line-height: 16px;
    color: var(--primary-black-color-300)
  }
}
.common-btn {
  color: var(--el-color-primary);
  box-sizing: border-box;
  min-width: 92px;
  height: 32px;
  font-weight: 600;
  border-radius: 4px;
  border-width: 1px;
  border-color: var(--el-color-primary);
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  padding: 0 6px;
}
/** 图表中标题样式 */
.chart-title {
  color: var(--primary-black-color-600);
  font-size: 14px;
  font-weight: 600;
  line-height: 20px;
  position: absolute;
  left: 16px;
  top: 16px;
  z-index: 9;
  display: flex;
  align-items: center;
}
/** 图表中标题样式 */
.chart-options {
  font-size: 14px;
  line-height: 20px;
  position: absolute;
  right: 16px;
  top: 16px;
  z-index: 9;
  display: flex;
  align-items: center;
}
/** 状态盒子样子-普通 */
.status-box {
  width: 64px;
  display: block;
  height: 24px;
  line-height: 24px;
  font-size: 12px;
  margin: auto;
  border-radius: 2px;
  font-weight: 500;
}
/** 状态盒子样子-小 */
.status-box-mini {
  width: 56px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  border-radius: 2px;
  font-weight: 600;
}
.red-status {
  background-color: rgba(229, 75, 23, 0.20);
  color: #E54B17;
}
.orange-status {
  background-color: rgba(229, 144, 0, 0.20);
  color: #E59000;
}
.yellow-status {
  background-color: rgba(255, 255, 0, 0.20);
  color: #FFCC00;
}
.green-status {
  background-color: rgba(19, 191, 119, 0.20);
  color: #13BF77;
}
.blue-status {
  background-color: rgba(58, 124, 255, 0.2);
  color: #3A7CFF
}
.purple-status {
  background-color: rgba(114, 46, 209, 0.20);
  color: #722ED1;
}
.olivedrab-status {
  background-color: rgba(107, 142, 35, 0.2);
  color: #6b8e23
}
.crimson-status {
  background-color: rgba(153, 0, 0, 0.2);
  color: #990000
}
.navy-status {
  background-color: rgba(0, 0, 128, 0.2);
  color: #000080
}
.saddlebrown-status {
  background-color: rgba(139, 69, 19, 0.2);
  color: #8b4513
}
.gray-status {
  background-color: rgba(98, 99, 102, 0.20);
  color: #626366
}
/* 模块标题 */
.module-title {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  flex-shrink: 0;
  width: 100%;
  height: 56px;
  padding: 0 16px;
  border-bottom-width: 1px;
  background-color: #fff;
  font-size: 16px;
  font-weight: 600;
}
/* 模块主体 纵向布局 */
.module-container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  /* 56px是顶部标题栏高度 */
  height: calc(100vh - 56px);
  margin: 0;
  padding: 16px;
  background-color: #f2f3f5;
  font-size: 13px;
  > .el-scrollbar__bar.is-vertical {
    top: 18px;
  }
  .el-table {
    font-size: 13px;
    .caret-wrapper {
      display: none;
    }
  }
}
/* 模块主体 内容器 */
.module-container-inner {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  flex: auto;
  margin: 0;
  padding: 0;
  border-radius: 4px;
  background-color: #fff;
}
/* 模块主体 横向布局 */
.module-main {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: stretch;
  flex: 1;
  overflow-x: auto;
  width: 100%;
  /* 56px是顶部标题栏高度 */
  height: calc(100vh - 56px);
  margin: 0;
  padding: 16px;
  background-color: #f2f3f5;
  font-size: 13px;
  &.module-vertical {
    flex-direction: column;
  }
  &.module-main-inner {
    margin-top: 20px;
    padding: 0;
  }
  &.module-main-scroll {
    padding: 0;
  }
}
/* 模块侧边栏 */
.module-aside {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  flex-shrink: 0;
  width: 300px;
}
/* 侧边列表 */
/* 这里不是指整个页面左侧的导航菜单 */
/* 而是指模块内容里面靠左的具体业务列表 */
.aside-list {
  flex: 1;
  overflow-y: auto;
  margin-top: 20px;
  margin-right: -14px;
}
/* 侧边列表每项 */
.aside-item {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
  margin-bottom: 10px;
  padding: .5em;
  border: 2px solid transparent;
  border-radius: 4px;
  background-color: #fff;
  text-align: left;
  cursor: pointer;
  /* 侧边列表每项 鼠标悬浮 */
  &:hover {
    border: 2px solid #529cfd;
    background-color: #f9f9f9;
    cursor: pointer;
  }
  /* 侧边列表每项 当前激活 */
  &.aside-item--active {
    border: 2px solid #529cfd;
  }
}
/* 侧边列表容器 */
.aside-list-box {
  width: 258px;
  flex-grow: 0;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  padding: 16px 0 0;
  background: #fff;
  height: 100%;
}
/* 侧边列表每项 未选中 */
.aside-normal-item {
  position: relative;
  flex-shrink: 0;
  width: 226px;
  margin-top: 8px;
  border: 1px solid #C8C9CC;
  border-radius: 4px;
  font-size: 13px;
  color: #313233;
  text-align: left;
  /* 内层容器 */
  .aside-normal-item-inner {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    flex-shrink: 0;
    padding: 8px;
    & > * {
      margin-top: 8px;
      &:first-child {
        margin-top: 0;
      }
    }
  }
  /* 悬浮列表某项时 显示悬浮按钮 */
  &:hover .aside-item-btn-box {
    display: flex;
  }
  /* 悬浮按钮 */
  .aside-item-btn-box {
    position: absolute;
    top: 0;
    right: 0;
    display: none;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    width: 20px;
    height: 100%;
    border-radius: 0 4px 4px 0;
    background-color: #d6d6d6;
    span {
      width: 20px;
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      &:hover {
        background-color: var(--el-color-primary);
      }
    }
  }
}
/* 侧边列表每项 选中 */
.aside-active-item {
  border-color: #165DFF;
  color: #165DFF;
}
/* 模块主体内容 */
.module-content {
  flex: auto;
  width: auto;
  margin-left: 24px;
  margin-right: -14px;
}
/* 子模块 */
.submodule {
  overflow: hidden;
  margin-bottom: 16px;
  border-width: 1px;
  border-radius: 4px;
  background-color: #fff;
  text-align: left;
}
/* 信息面板 容器 */
.info-box {
  margin: 0 20px;
  /* 信息面板 单行从左往右排列 */
  &.info-box-row {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: nowrap;
    width: 100%;
  }
}
/* 信息面板每项 */
.info-item {
  flex: none;
  margin-right: 16px;
  font-size: 13px;
}
/* 信息面板每项 标题 深色 */
.info-title-deep {
  flex-shrink: 0;
  color: #313233;
  font-size: 13px;
}
.info-title-deep-large {
  flex-shrink: 0;
  color: #313233;
  font-size: 16px;
  font-weight: 600;
}
/* 信息面板每项 标题 */
.info-title {
  flex-shrink: 0;
  color: #969799;
  line-height: 20px;
  font-size: 13px;
}
/* 信息面板 数字 */
.info-data-content {
  color: #313233;
  font-size: 20px;
  line-height: 20px;
  font-style: normal;
  font-weight: 700;
  text-align: left;
}
/* 信息面板，内置 */
.info-data-box {
  padding: 12px 16px;
  display: flex;
  justify-content: space-around;
  border-radius: 4px;
  background: #f5f7fa;
}
.info-data-box-inner {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 4px;
  gap: 4px;
}
/* 信息面板每项 内容 */
.info-content {
  color: #626366;
  word-break: break-word;
  font-size: 13px;
  text-align: left;
}
/* 信息面板每项 内容 深色文本 */
.info-content-dark {
  color: #313233;
  word-break: break-word;
  font-size: 13px;
  text-align: left;
}
/* 表单分组 标题 */
.form-section-title {
  position: relative;
  margin: 20px 0;
  padding-bottom: 6px;
  font-weight: bold;
  text-align: left;
  /* 底边框 */
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    display: block;
    overflow: hidden;
    width: 10em;
    max-width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: #529cfd;
  }
}
/* element文本框的右下角字数限制提示不要挡住文本框内容 */
.el-textarea textarea {
  padding-bottom: 1.5em;
}
.el-textarea .el-input__count {
  padding-right: .5em;
}
.el-drawer {
  .el-drawer__header {
    margin: 0;
    padding-top: 0;
    border-bottom: 1px solid var(--primary-black-color-200);
  }
  .el-drawer__body {
    padding: 0;
  }
  .el-drawer__footer {
    padding: 10px 12px;
    border-top: 1px solid var(--primary-black-color-200);
  }
}
.el-button--primary {
  background: linear-gradient(90deg, #0167FF 0%, #019FFF 100%);
  &.is-disabled, &.is-disabled:focus, &.is-disabled:hover {
    background: linear-gradient(90deg, #a0cfff 0%, #bcddfd 100%);
    border-color: #e3edf7;
  }
  &.is-link {
    background: unset;
    &:hover, &:focus, &.is-disabled, &.is-disabled:focus, &.is-disabled:hover {
      background: unset;
    }
  }
  &.is-plain {
    background: unset;
    border-color: var(--el-color-primary);
    &.is-disabled, &.is-disabled:focus, &.is-disabled:hover {
      background: unset;
    }
  }
  &:hover, &:focus {
    background: linear-gradient(90deg, #0167FF 0%, #019FFF 100%)
  }
}
.el-button {
  .el-icon:has(~span) {
    margin-right: 4px;
  }
  [class*=el-icon] + span {
    margin-left: 0 !important;
  }
}
.dialog-form {
  font-size: 13px;
  .el-button {
    font-size: 13px;
  }
  .el-table {
    font-size: 13px;
    .cell {
      padding: 0 8px;
    }
  }
  .el-form {
    color: var(--primary-black-color-600);
    width: 100%;
    .el-form-item {
      margin-bottom: 14px;
      &:first-child {
        margin-top: 14px;
      }
      .el-input__wrapper {
        width: 100%
      }
    }
    .el-form-item__label {
      padding-right: 0;
      font-size: 13px;
    }
    .el-form-item__content {
      font-size: 13px;
    }
  }
}
/* 子模块 */
.card-box {
  position: relative;
  padding: 16px;
  border-radius: 4px;
  background-color: #fff;
  display: flex;
  align-items: center;
  /* 子模块 标题 */
  .title {
    font-size: 15px;
    font-weight: 600;
    line-height: 20px;
    text-align: left;
    align-self: start;
  }
}
/* 搜索模块 */
.search-box {
  position: relative;
  padding: 16px;
  border-radius: 4px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  .label {
    flex-shrink: 0;
    flex-grow: 0;
    width: 30px;
    font-size: 13px;
    color: #969799;
    text-align: left;
  }
  :deep(.el-input__wrapper) {
    width: 100%;
  }
  .el-select {
    width: 100%;
  }
  .el-date-editor.el-input, .el-date-editor.el-input__wrapper {
    width: 100%;
  }
  .item {
    height: 32px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .item-col {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    .label {
      width: 100%;
      line-height: 20px;
      margin-bottom: 4px;
    }
  }
  .item-btn {
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
    padding-bottom: 6px;
  }
}
/* 表单弹窗 顶部 */
.form-dialog-header {
  font-size: 16px;
  font-weight: 600;
  color: #313233;
  text-align: left;
}
/* 表单弹窗 主体外壳 */
.form-dialog-main {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  padding: 0 12px;
  .el-form {
    .el-form-item {
      margin: 0;
      padding: 8px 0;
    }
    .el-form-item__label {
      color: #666;
    }
    .el-form-item__content {
      & > * {
        width: 100%;
      }
    }
  }
}
/* 表单弹窗 主体内容 */
.form-dialog-content {
  flex: auto;
  height: auto;
  max-height: 70vh;
  text-align: left;
  padding-top: 16px;
}
/* 表单弹窗 底部 */
.form-dialog-bottom {
  flex: none;
}
/* 表单弹窗 底部 新 */
.form-dialog-footer {
  flex: none;
  padding: 0 8px;
}
/* 表单分组 */
.form-section {
  position: relative;
  width: 100%;
}
/* 表单分组 顶部 */
.form-section-header {
  font-size: 13px;
  font-weight: 600;
  color: #313233;
}
/* 展开收起图标 梯形背景 */
.trapezoid {
  display: flex;
  justify-content: center;
  align-items: center;
  align-self: center;
  width: 92px;
  height: 15px;
  padding-bottom: 3px;
  background-color: #fff;
  cursor: pointer;
  clip-path: polygon(0 0, 100% 0, 82% 100%, 18% 100%);
}
/* 话术音频弹窗 */
/* 话术审核弹窗 */
/* 商户弹窗 */
/* 线路供应商弹窗 */
/* 短信供应商弹窗 */
/* 表单确认弹窗 */
/* 数据过滤弹窗 */
/* 语义弹窗 */
.script-audio-dialog,
.script-check-dialog,
.merchant-dialog,
.supplier-dialog,
.sms-dialog,
.restriction-dialog,
.form-confirm-dialog,
.semantic-dialog {
  font-size: 13px;
  text-align: left;
  .el-dialog__header,
  .el-dialog__body,
  .el-dialog__footer {
    padding: 0;
  }
  .el-button + .el-button {
    margin-left: 20px;
  }
  .form-dialog-header {
    padding: 12px 16px;
  }
  .form-dialog-main {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    max-height: 70vh;
    padding: 0;
  }
  /* 弹窗主体 内层容器 */
  .form-dialog-main-inner {
    width: 100%;
    height: 100%;
    padding: 16px 24px;
  }
  .form-dialog-content {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 12px 24px;
  }
  .form-dialog-footer {
    padding: 16px 24px;
    text-align: right;
  }
  .el-form-item__label {
    padding: 0;
  }
}
/* 商户管理 编辑线路 线路组弹窗 */
.merchant-line-supplier-line-dialog {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  flex-wrap: nowrap;
  height: 100%;
  max-height: 95vh;
  /* Element样式 */
  .el-dialog__header {
    flex: none;
  }
  .el-dialog__body {
    flex: auto;
    overflow: hidden;
  }
  .el-dialog__footer {
    flex: none;
  }
  /* 弹窗主体 外层容器 */
  .form-dialog-main {
    height: 100%;
    max-height: 100%;
  }
  /* 弹窗主体 内层容器 */
  .form-dialog-main-inner {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    flex-wrap: nowrap;
    height: 100%;
    .el-form-item {
      flex: none;
    }
    /* 表格 */
    #supplier-line-table {
      flex: auto;
      /* 单元格 */
      .el-table__cell {
        padding: 8px 0;
      }
    }
    /* 卡片 */
    #supplier-line-card-list {
      flex: auto;
    }
  }
}
/* 商户线路详情 */
/* 供应线路详情 */
/* 供应线路抽屉 */
/* 短信对接账号详情 */
.submodule-detail {
  font-size: 13px;
  text-align: left;
  /* Element样式 */
  .el-button + .el-button {
    margin-left: 20px;
  }
  .el-drawer__header {
    padding: 12px 16px;
  }
  .el-drawer__body,
  .el-drawer__footer {
    padding: 0;
  }
  .el-form-item {
    margin: 0 80px 0 0;
    padding: 8px 0;
  }
  .el-form--inline .el-form-item {
    vertical-align: sub;
  }
  .el-form-item__label {
    padding: 0;
  }
  .el-cascader__tags .el-tag {
    margin: 0 0 0 4px;
  }
  /* 详情主体 */
  .detail-main {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    flex-wrap: nowrap;
    width: 100%;
    min-width: 1080px;
    margin: 0;
    padding: 16px;
    background-color: #f2f3f5;
  }
  /* 详情底部 */
  .detail-footer {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: stretch;
    flex-wrap: nowrap;
    width: 100%;
    padding: 16px;
    background-color: #fff;
    text-align: right;
  }
  /* 抽屉顶部 */
  .drawer-header {
    flex: none;
    width: 100%;
    margin: 0 auto;
    color: #000;
    font-size: 14px;
    font-weight: 600;
  }
  /* 抽屉主体 外层容器 */
  .drawer-main {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    flex-wrap: nowrap;
    margin: 0;
    padding: 0;
    background-color: #fff;
  }
  /* 抽屉主体 内层容器 */
  .drawer-main-inner {
    width: 100%;
    height: 100%;
    margin: 0 auto;
    padding: 16px;
    border-radius: 4px;
    background-color: #fff;
  }
  /* 抽屉底部 */
  .drawer-footer {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: stretch;
    flex-wrap: nowrap;
    width: 100%;
    margin: 0 auto;
    padding: 16px;
    background-color: #fff;
    text-align: right;
  }
  /* 表单区块 */
  .form-block {
    position: relative;
    /* margin: 0; */
    padding: 16px;
    border-radius: 4px;
    background-color: #fff;
  }
  /* 表单区块标题 */
  .form-block-title {
    color: #313233;
    font-size: 14px;
    font-weight: 600;
    line-height: 22px;
  }
}
/* 坐席工作台 自动签出任务通知 */
.auto-check-out-task-notification {
  &.right {
    --el-notification-width: 360px;
    --el-notification-close-font-size: 20px;
    --el-notification-title-font-size: 16px;
    --el-notification-content-font-size: 16px;
    /* 标题 */
    .el-notification__title {
      text-align: left;
    }
    /* 内容 */
    .el-notification__content {
      margin: 20px 0;
    }
    /* 关闭按钮 */
    .el-notification__closeBtn {
      top: 14px;
    }
  }
}
/* 坐席工作台 关联任务启动通知 */
.linked-task-start-notification {
  text-align: left;
  transform: translate(8px, 32px);
}
/* 坐席工作台 新线索分配通知 */
.new-clue-notification {
  text-align: left;
  transform: translate(8px, 32px);
}
/* 呼叫弹窗 */
.calling-dialog {
  &.el-dialog {
    position: fixed;
    top: 56px;
    right: 16px;
    overflow: hidden;
    margin: 0;
    border-radius: 4px;
  }
  .el-dialog__header {
    margin: 0;
    padding: 0;
  }
  .el-dialog__body {
    padding: 0;
  }
}
.ant-btn-primary {
  background-color: #1677ff;
}
/* 坐席监控 弹窗 */
/* 坐席工作台 弹窗 */
.seat-monitor-dialog,
.seat-workbench-dialog {
  .el-dialog__header,
  .el-dialog__body,
  .el-dialog__footer {
    padding: 0;
  }
  .form-dialog-header {
    padding: 12px 16px;
  }
  .form-dialog-main {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;
    padding: 12px 16px;
    text-align: left;
  }
  .form-dialog-content {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    padding: 0;
  }
  .form-dialog-footer {
    padding: 16px 24px;
    text-align: right;
  }
}
/* 签入任务弹窗 */
.check-in-task-dialog {
  .form-dialog-main {
    padding: 12px 24px;
  }
  .form-dialog-content {
    height: 500px;
    max-height: 70vh;
  }
}
/* 呼叫弹窗 */
.calling-dialog {
  .form-dialog-main {
    padding: 12px 16px;
  }
  .form-dialog-footer {
    padding: 8px 24px;
  }
}
/* 呼叫失败弹窗 */
.call-fail-dialog {
  .form-dialog-main {
    padding: 16px;
    border-bottom: 1px solid #E1E3E6;
  }
}
/* 退出监听弹窗 */
.exit-monitor-dialog {
  .form-dialog-main {
    padding: 12px 24px;
  }
}
/* 挂断电话弹窗 */
.hangup-dialog {
  .form-dialog-main {
    padding: 16px;
  }
}
/* 文本框内的标签 */
.el-cascader__tags > .el-tag {
  margin: 0 0 0 4px !important;
}
/* 加载图标 */
.loading-icon {
  /* 顺时针旋转 */
  animation: clockwise-rotate 2s linear infinite;
  @keyframes clockwise-rotate {
    0% {
      transform: rotate(0deg);
    }
    50% {
      transform: rotate(180deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}
/* 短信预览弹窗 */
.sms-preview-dialog {
  &.el-dialog {
    background: transparent;
  }
  .el-dialog__body {
    border: none;
  }
}

/* 接待时长警告消息 */
.reception-time-warning {
  &.el-message {
    --el-message-close-size: 16px;
    --el-message-close-hover-color: #909399;
    z-index: 3000;
    min-width: 380px;

    .el-message__content {
      font-size: 14px;
      font-weight: 500;
      line-height: 1.4;
    }

    .el-message__closeBtn {
      font-size: 16px;
      cursor: pointer;

      &:hover {
        color: var(--el-message-close-hover-color);
      }
    }
  }
}
