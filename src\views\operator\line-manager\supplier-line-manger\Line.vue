<template>
  <div class="line-manger-container">
    <div class="tw-w-full tw-bg-white tw-p-[16px] tw-grow-0 tw-shrink-0">
      <div class="tw-grid tw-grid-cols-5 2xl:tw-grid-cols-7 tw-gap-3 tw-border-b-[1px] tw-pb-[12px]">
        <div class="item">
          <el-input
            v-model.trim="searchForm.supplyLineName"
            placeholder="线路名称"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-input
            v-model.trim="searchForm.supplyLineNumber"
            placeholder="线路编号"
            clearable
            @keyup.enter="search"
          />
        </div>
        <div class="item">
          <el-select v-model="searchForm.enableStatus" placeholder="生效状态" clearable>
            <el-option
              v-for="lineStatusItem in Object.values(supplierLineStatusList)"
              :key="lineStatusItem.name"
              :value="lineStatusItem.val"
              :label="lineStatusItem.text"
            />
          </el-select>
        </div>
        <!-- <div class="item">
          <el-select class="tw-w-full" v-model="searchForm.isForEncryptionPhones" placeholder="请选择数据传输方式" style="width: 240px;">
            <el-option
              v-for="item in dataEncryptionMethodOption"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            />
          </el-select>
        </div> -->
        <template v-if="isExpand">
          <div class="item">
            <el-input
              v-model.trim="searchForm.masterCallNumber"
              placeholder="主叫号码"
              clearable
              @keyup.enter="search"
            />
          </div>
          <div class="item">
            <el-input
              v-model.trim="searchForm.callLineSupplierName"
              placeholder="供应商名称"
              clearable
              @keyup.enter="search"
            />
          </div>
          <div class="item">
            <el-input
              v-model.trim="searchForm.callLineSupplierNumber"
              placeholder="供应商编号"
              clearable
              @keyup.enter="search"
            />
          </div>
          <div class="item">
            <el-select
              v-model="searchForm.serviceProviders"
              placeholder="支持运营商"
              multiple
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="1"
              clearable
            >
              <el-option
                v-for="operatorItem in Object.values(supplierOperatorList)"
                v-show="operatorItem.name!==supplierOperatorList.all!.name"
                :key="operatorItem.name"
                :value="operatorItem.val"
                :label="operatorItem.text"
              />
            </el-select>
          </div>
          <div class="item">
            <el-select
              v-model="searchForm.primaryIndustries"
              placeholder="适用行业"
              multiple
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="1"
              clearable
            >
              <el-option
                v-for="industryItem in primaryIndustryList"
                :key="industryItem"
                :value="industryItem"
                :label="industryItem"
              />
            </el-select>
          </div>
          <div class="item">
            <el-cascader
              v-model="searchForm.cityCodes"
              :options="cityCodeOptions"
              :props="props"
              :show-all-levels="false"
              collapse-tags
              filterable
              collapse-tags-tooltip
              style="width:100%"
              placeholder="支持外呼区域"
              clearable
            />
          </div>
          <div class="item">
            <el-input
              v-model="searchForm.displayCallNumber"
              placeholder="外显号码"
              clearable
              @keyup.enter="search"
            />
          </div>
        </template>
      </div>
      <div class="tw-flex tw-justify-end tw-mt-[12px]">
        <el-button type="primary" @click="clearSearchForm" link>
          <el-icon size="--el-font-size-base"><SvgIcon name="reset" color="var(--el-color-primary)" /></el-icon>
          <span>重置</span>
        </el-button>
        <el-button type="primary" @click="search" link>
          <el-icon size="--el-font-size-base" color="var(--el-color-primary)"><SvgIcon name="search" color="none" /></el-icon>
          <span>查询</span>
        </el-button>
        <el-button type="primary" v-if="isExpand" @click="isExpand=false" link>收起<el-icon size="--el-font-size-base"><ArrowUp /></el-icon></el-button>
        <el-button type="primary" v-else @click="isExpand=true" link>展开<el-icon size="--el-font-size-base"><ArrowDown /></el-icon></el-button>
      </div>
    </div>
    <SupplyLineTable
      v-loading="loading"
      :tableData="tableData||[]"
      showPagination
      showEditPending
      showDetails
      canGoSupplier
      class="tw-flex-grow"
      @update:table="search"
    >
      <template v-slot:operate="{ row }">
        <el-button type="primary" link @click="goEdit(row)">编辑</el-button>
        <el-button type="primary" link @click="goDetail(row)">详情</el-button>
      </template>
    </SupplyLineTable>
  </div>
  <SupplierLineDrawer
    v-if="supplierStore.editingSupplierLine.id && supplierStore.editingSupplierLine.id > 0"
    :id="supplierStore.editingSupplierLine.id!"
    :content="supplierStore.editingSupplierLine"
    :supplier="supplierStore.currentSupplier"
    :visible="dialogLineVisible"
    :readonly="true"
    @modify="handleModifyLine"
    @close="dialogLineVisible=false"
  />
</template>

<script lang="ts" setup>
import { reactive, computed, ref, onDeactivated, onActivated, defineAsyncComponent, } from 'vue'
import { ElMessage, } from 'element-plus'
import { ArrowUp, ArrowDown } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { useSupplierStore } from '@/store/supplier'
import { useGlobalStore } from '@/store/globalInfo'
import {
  supplierLineStatusList,
  supplierOperatorList,
} from '@/assets/js/map-supplier'
import router from '@/router'
import SupplyLineTable from '@/components/SupplyLineTable.vue'
import { SupplierLineParams, SupplierLineInfoItem, } from '@/type/line'
import { lineSupplierModel } from '@/api/line'
import { goSupplyLinDetail } from '@/utils/line'
import { useRoute } from 'vue-router'

const globalStore = useGlobalStore()
const { loading, primaryIndustryList } = storeToRefs(globalStore)
// 导入供应线路查看抽屉
const SupplierLineDrawer = defineAsyncComponent(() => {
  return import('@/views/operator/supplier-manager/line-supplier/SupplierLineDrawer.vue')
})

const dataEncryptionMethodOption = [
  {label: '普通', value: false},
  {label: '加密', value: true},
]

// 用于编辑、查看供应商信息放到本地缓存
const supplierStore = useSupplierStore()

const props = {multiple: true, emitPath: false}
const tableData = ref<SupplierLineInfoItem[]>([])

// 搜索
class searchOrigin {
  callLineSupplierName = undefined
  callLineSupplierNumber = undefined
  enableStatus = undefined
  serviceProviders = undefined
  primaryIndustries = undefined
  displayCallNumber = undefined
  masterCallNumber = undefined
  supplyLineNumber = undefined
  supplyLineName = undefined
  cityCodes = undefined
  isForEncryptionPhones = undefined
}
const searchForm = reactive<SupplierLineParams>(new searchOrigin())
const search = async () => {
  loading.value = true
  tableData.value = (await lineSupplierModel.getLineList(searchForm) || []) as SupplierLineInfoItem[]
  loading.value = false
}
// 重置
const clearSearchForm = () => {
  Object.assign(searchForm, new searchOrigin())
  ElMessage({
    type: 'success',
    message: '重置成功'
  })
}
// 收起、展开搜索条件
const isExpand = ref(false)

// 表格 -- 前往【编辑】
const dialogLineVisible = ref(false)
const goEdit = async (row: SupplierLineInfoItem) => {
  await goSupplyLinDetail({
    supplyLineNumber: row.lineNumber,
    supplierNumber: row.callLineSupplierNumber,
  }, true)
}
// 表格 -- 前往【详情】
const goDetail = async (row: SupplierLineInfoItem) => {
  router.push({
    name: 'SupplierLineMonitorDetails',
    query: {
      supplyLineNumber: row.lineNumber,
      supplyLineName: row.lineName,
      supplierNumber: row.callLineSupplierNumber,
    }
  })
}
const handleModifyLine = () => {
  dialogLineVisible.value = false
  router.push({ name: 'SupplierLineDetail' })
}
// 初始化信息
const cityCodeOptions = ref<{
  label: string, value: string, children: {label: string, value: string}[]
}[]>([])
const init = async () => {
  cityCodeOptions.value = []
  globalStore.provinceAllMap && Object.keys(globalStore.provinceAllMap)?.length > 0
  && Object.keys(globalStore.provinceAllMap).map(item => {
    const arr = item.split(',')
    cityCodeOptions.value.push({
      label: arr[0]!,
      value: arr[1]!,
      children: globalStore.provinceAllMap[item]?.map(child => {
        const childArr = child.split(',')
        return {
          label: childArr[0]!,
          value: childArr[1]!,
        }
      }) || []
    })
  })
  search()
}

onActivated(() => {
  const route = useRoute();
  searchForm.supplyLineNumber = route.query.supplyLineNumber as string || ''
  if (!tableData.value || tableData.value.length < 1) {
    init()
  }
})

const clearAllData = () => {
  // @ts-ignore
  cityCodeOptions.value = null
  // @ts-ignore
  tableData.value = null
}

onDeactivated(() => {
  clearAllData()
})
</script>

<style scoped lang="postcss" type="text/postcss">
.line-manger-container {
  width: 100%;
  box-sizing: border-box;
  height: calc(100% - 48px);
  display: flex;
  flex-direction: column;
  .item {
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    .label {
      width: 120px
    }
    :deep(.el-input__wrapper) {
      width: 100%;
    }
    .el-select {
      width: 100%;
    }
  }
  .el-table {
    font-size: 13px;
  }
  .table-btn-box {
    display: flex;
    .el-button {
      width: 60px;
    }
  }
  :deep(.el-cascader__search-input) {
    min-width: 20px;
    flex-grow: 0;
    flex-shrink: 1
  }
}
</style>
